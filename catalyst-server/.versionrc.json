{"tag-prefix": "opp-1-5-", "release-count": 5, "no-verify": true, "dry-run": false, "infile": "./cicd/docs/CHANGELOG.md", "releaseCommitMessageFormat": "build: inc'd test chore(release): {{currentTag}} [skip ci]", "types": [{"type": "feat", "section": "Features"}, {"type": "fix", "section": "Bug Fixes"}, {"type": "test", "section": "Tests", "hidden": true}, {"type": "build", "section": "Build System", "hidden": true}, {"type": "ci", "hidden": true}, {"type": "chore", "hidden": true}, {"type": "docs", "section": "Features"}, {"type": "perf", "section": "Features"}, {"type": "refactor", "section": "Features"}, {"type": "revert", "hidden": true}, {"type": "style", "section": "Features"}]}