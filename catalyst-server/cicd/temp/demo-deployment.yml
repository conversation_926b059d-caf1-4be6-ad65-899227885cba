# This yml file is used by Azure DevOps to execute automated tests against PR's
# and deploy changes to the test environment once they are merged into a "IN_Sprint_*" branch
# The deployment includes pushing new server images to catalystinnovation container registry
# hosted in the ACME Gov cloud as well as pushing cluster updates to Kubernetes containing
# the deployed image to use.
#
# This file serves two purposes, validation of automated tests for pull requests and
# automated deployed to our hosted azure gov cloud instance. It also produces deployment
# artifacts for debugging issues. Rollback procedures are in place for failures to revert
# tags, images or cluster updates that should not be used due to failure.
#
# Testing CICD Build: When testing the pipeline flow, set "deployChanges" to false. This will prevent
# any changes being pushed to any server. Setting this flag to false will execute all steps excepts 
# the ones that pertain to commiting code, pushing tags or releasing versions. The variable can be found
# under our Library in Azure DevOps named Global-Variables:
# https://dev.azure.com/ACME-General/catalyst-innovation/_library?itemType=VariableGroups
#
# Created by: Alan Resha
trigger:
  branches:
    include:
      - opportunity_1_5

variables:
  - name: pipelineVersion
    ${{ if eq(variables['Build.Reason'], 'PullRequest') }}:
      value: 'PR_'
    ${{ else }}:
      value: ''
  - name: buildDate
    value: $(date '+%Y%m%d')

name: $(pipelineVersion)$(buildDate)

stages:
  - stage: TestCatalystServer
    displayName: 'Automated Testing'
    jobs:
      - job: test
        displayName: 'Run automated-testing'
        continueOnError: false
        workspace:
          clean: all
        steps:
          - task: UseNode@1
            inputs:
              version: $(nodeVersion)
            displayName: 'Install Node.js'
          
          - template: ../templates/stepsRunCatalystServerTests.yml
            parameters:
              workingDirectory: $(System.DefaultWorkingDirectory)/catalyst-server

          - task: PublishTestResults@2
            displayName: 'Publish testing report'
            inputs:
              testRunner: JUnit
              testResultsFiles: '**/junit-reporter.xml'

  - stage: build
    displayName: 'Tag version and build'
    dependsOn: TestCatalystServer
    condition: and(ne(variables['Build.Reason'], 'PullRequest'), succeeded('TestCatalystServer'))
    jobs:
      - job: versionBuildRelease
        displayName: 'Checkout, Build and version the applications'
        continueOnError: false
        workspace:
          clean: all
        steps:
          - template: ../templates/stepsCheckoutAndPullBranch.yml

          - task: Bash@3
            inputs:
              targetType: 'inline'
              script: |
                echo "Remove all previous built images"
                docker image rmi cpicregistry.azurecr.us/catalyst-server || true
              workingDirectory: $(System.DefaultWorkingDirectory)
            displayName: 'Remove old images'

          - task: Npm@1
            displayName: "Install catalyst-innovation-client dependencies"
            inputs:
              command: custom
              customCommand: 'ci'
              workingDir: $(System.DefaultWorkingDirectory)/catalyst-innovation-client

          - task: Npm@1
            displayName: "Build catalyst-innovation-client"
            inputs:
              command: custom
              customCommand: 'run prod-build-web'
              workingDir: $(System.DefaultWorkingDirectory)/catalyst-innovation-client

          - task: Npm@1
            displayName: "Install catalyst-curator-client dependencies"
            inputs:
              command: custom
              customCommand: 'ci'
              workingDir: $(System.DefaultWorkingDirectory)/catalyst-curator-client

          - task: Npm@1
            displayName: "Build catalyst-curator-client"
            inputs:
              command: custom
              customCommand: 'run prod-build-web'
              workingDir: $(System.DefaultWorkingDirectory)/catalyst-curator-client

          - task: Npm@1
            displayName: "Install catalyst-server dependencies"
            inputs:
              command: ci
              workingDir: $(System.DefaultWorkingDirectory)/catalyst-server

          - task: Bash@3
            inputs:
              targetType: 'inline'
              script: |
                PREV_PACKAGE_VERSION=$(node -p "require('./package.json').version")
                npm run release
                PACKAGE_VERSION=$(node -p "require('./package.json').version")
                npm version ${PACKAGE_VERSION}
                TAG_PREFIX=$(node -p "require('./.versionrc.json')['tag-prefix']")
                TAG_NAME="$TAG_PREFIX$PACKAGE_VERSION"
                echo "Tag Name: $TAG_NAME"
                echo "##vso[task.setvariable variable=PREV_PACKAGE_VERSION;isoutput=true]$PREV_PACKAGE_VERSION"
                echo "##vso[task.setvariable variable=PACKAGE_VERSION;isoutput=true]$PACKAGE_VERSION"
                echo "##vso[task.setvariable variable=TAG_NAME;isoutput=true]$TAG_NAME"
                echo "##vso[build.updatebuildnumber]"${PACKAGE_VERSION}_$(buildDate)""
              workingDirectory: $(System.DefaultWorkingDirectory)/catalyst-server
            name: createGitHubTag
            displayName: 'Create GitHub tagged version'

          - task: Npm@1
            displayName: "Build catalyst-server"
            inputs:
              command: custom
              customCommand: 'run build'
              workingDir: $(System.DefaultWorkingDirectory)/catalyst-server

          - task: Npm@1
            displayName: "Copy web folders into catalyst-server directory"
            inputs:
              command: custom
              customCommand: 'run copy-web-build'
              workingDir: $(System.DefaultWorkingDirectory)/catalyst-server

          - task: DownloadSecureFile@1
            name: pullEnvFile
            displayName: 'Download azure corp env file'
            inputs:
              secureFile: '$(corp_env_file)'

          - task: CopyFiles@2
            name: copyCorpEnv
            displayName: 'Copy corp env file'
            inputs:
              SourceFolder: '$(Agent.TempDirectory)'
              Contents: '$(corp_env_file)'
              OverWrite: true
              TargetFolder: '$(System.DefaultWorkingDirectory)/catalyst-server'

          - task: Npm@1
            name: setCorpEnv
            displayName: "Set corp .env file"
            inputs:
              command: custom
              customCommand: 'run corp-env'
              workingDir: $(System.DefaultWorkingDirectory)/catalyst-server

          - task: Bash@3
            inputs:
              targetType: 'inline'
              script: |
                echo "Remove migration folders. We want to create fresh copies of the migration each build."
                echo "The prod folder will be recreated and updated with a fresh script. This simplifies rollback."
                rm -rf migrations || true
              workingDirectory: $(System.DefaultWorkingDirectory)/catalyst-server
              failOnStderr: false
            name: clearMigrationFolders
            displayName: 'Clear migration folders'

          - task: Bash@3
            inputs:
              targetType: 'inline'
              script: |
                echo "Create migration scripts for azure corp."
                echo "Working director: $(System.DefaultWorkingDirectory)/catalyst-server"
                pwd
                ls -la
                chmod +r .env
                NODE_ENV=production npm run dbCli create-migrations
              workingDirectory: $(System.DefaultWorkingDirectory)/catalyst-server
            name: runMigrationStaging
            displayName: 'Run azure-corp create-migration'
            condition: eq(variables.runMigration, true)

          - task: Bash@3
            inputs:
              targetType: 'inline'
              script: |
                az acr login --name cpicregistry
                docker build -f deploy/prod.Dockerfile -t cpicregistry.azurecr.us/catalyst-server:$(createGitHubTag.PACKAGE_VERSION) -t cpicregistry.azurecr.us/catalyst-server:latest .
              workingDirectory: $(System.DefaultWorkingDirectory)/catalyst-server
            name: buildCatalystServerImages
            displayName: 'Acr Login and Build Catalyst Server and API images'

          # NOTE: Uses deployChanges variable to prevenet pushing changes to GitHub
          - task: Bash@3
            inputs:
              targetType: 'inline'
              script: |
                git status
                git add ./migrations/*.ts
                git commit -an -m "Commit build changes [skip ci]"
                git merge origin/$(Build.SourceBranchName) -m "Merge published tag changes [skip ci]"
                if [ $(deployChanges) = true ]; then
                  git push --follow-tags origin $(Build.SourceBranchName)
                fi
                git status
              workingDirectory: $(System.DefaultWorkingDirectory)/catalyst-server
            name: pushChanges
            displayName: 'Push git changes'

          - task: CopyFiles@2
            inputs:
              sourceFolder: '$(System.DefaultWorkingDirectory)/catalyst-server'
              contents: |
                **
                !cicd/*.md
                !cicd/images/*.png
                !node_modules/**
                !.env*
              targetFolder: '$(Build.ArtifactStagingDirectory)'
              CleanTargetFolder: true
            displayName: 'Copy files used for build'

          - task: PublishPipelineArtifact@1
            inputs:
              artifactName: catalyst-server-opp-1_5
              targetPath: '$(Build.ArtifactStagingDirectory)'
              publishLocation: 'pipeline'
            displayName: 'Publish catalyst-server artifacts'

  - stage: stagingRelease
    displayName: 'Staging deployment'
    dependsOn: 
      - build
    condition: succeeded('build')
    jobs:
      - deployment: deployStaging
        displayName: 'Deploy changes to staging'
        environment: catalyst-server-opp-1_5
        continueOnError: false
        variables:
          newVersion: $[ stageDependencies.build.versionBuildRelease.outputs['createGitHubTag.PACKAGE_VERSION'] ]
        workspace:
          clean: all
        strategy:
          runOnce:
            deploy:
              steps:
                - checkout: none

                - task: DownloadSecureFile@1
                  name: azureCorpEnvFile
                  displayName: 'Download azure corp env file'
                  inputs:
                    secureFile: '$(corp_env_file)'
                
                - task: CopyFiles@2
                  displayName: 'Copy env file'
                  inputs:
                    SourceFolder: '$(Agent.TempDirectory)'
                    Contents: '$(corp_env_file)'
                    OverWrite: true
                    TargetFolder: '$(Pipeline.Workspace)/catalyst-server-opp-1_5'

                - task: CopyFiles@2
                  displayName: 'Copy env file to deploy'
                  inputs:
                    SourceFolder: '$(Agent.TempDirectory)'
                    Contents: '$(corp_env_file)'
                    OverWrite: true
                    TargetFolder: '$(Pipeline.Workspace)/catalyst-server-opp-1_5/deploy/'
                    
                - task: UseNode@1
                  inputs:
                    version: $(nodeVersion)
                  displayName: 'Install Node.js'

                - task: Npm@1
                  displayName: "Install catalyst-server dependencies"
                  inputs:
                    command: ci
                    workingDir: $(Pipeline.Workspace)/catalyst-server-opp-1_5

                - task: Npm@1
                  displayName: "Set .env file"
                  inputs:
                    command: custom
                    customCommand: 'run corp-env'
                    workingDir: $(Pipeline.Workspace)/catalyst-server-opp-1_5

                - task: Bash@3
                  inputs:
                    targetType: 'inline'
                    script: |
                      echo "Updating schema."
                      NODE_ENV=production npm run dbCli run-migrations
                    workingDirectory: $(Pipeline.Workspace)/catalyst-server-opp-1_5
                  name: miroStagingMigrateUp
                  displayName: 'Run Migration Up'
                  condition: and(eq(variables.deployChanges, true), eq(variables.runMigration, true))

                - template: ../templates/stepsBashUpdateAllTenantConfigs.yml
                  parameters:
                    workingDirectory: $(Pipeline.Workspace)/catalyst-server-opp-1_5

                # NOTE: Uses the deployChanges enviroment variable to for pushing the new image to the repository
                - task: Bash@3
                  inputs:
                    targetType: 'inline'
                    script: |
                      az acr login --name cpicregistry
                      if [ $(deployChanges) = true ]; then
                        docker push cpicregistry.azurecr.us/catalyst-server:$(newVersion)
                        docker push cpicregistry.azurecr.us/catalyst-server:latest
                      fi
                    workingDirectory: $(Pipeline.Workspace)/catalyst-server-opp-1_5
                  name: buildCatalystServerImages
                  displayName: 'Acr Login and Push Catalyst Server images'
