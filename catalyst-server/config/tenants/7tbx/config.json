{"applicationTitle": "Expeditionary Innovation", "company": "Expeditionary", "division": "7TBX", "favicon": "favicon", "sLogo": "https://catalystserverstorage.blob.core.usgovcloudapi.net/public/7tbx/sLogo.png", "mLogo": "https://catalystserverstorage.blob.core.usgovcloudapi.net/public/7tbx/mLogo.png", "lLogo": "https://catalystserverstorage.blob.core.usgovcloudapi.net/public/7tbx/lLogo.png", "logo": "https://catalystserverstorage.blob.core.usgovcloudapi.net/public/7tbx/logo.png", "backdropImage": "https://catalystserverstorage.blob.core.usgovcloudapi.net/public/7tbx/backdropImage.jpeg", "submission": {"applicationTitle": "Expeditionary Innovation", "welcomeTitle": "Expeditionary\nInnovation Center", "text1": "", "text2": "", "text3": "Not for reporting SHARP/EO/EEO reports or Emergency Situations. Those reports should be sent through the chain of command or appropriate authorities/agencies.\n\n", "text4": "SHARP contacts:\nBDE:\n24 Hour Hotline: (757) 268-8948\n8<PERSON><PERSON>, <PERSON> (757) 268-6766\n<PERSON>, <PERSON>  (757) 812-9318\n\nPost:\nMr. <PERSON> (757) 878-3727, ext 25\nMr. <PERSON> (757) 878-0022\n\nEO:\n<PERSON><PERSON>, <PERSON> (757) 848-8960", "text5": "Your problem statement will be reviewed by your Brigade or Division Innovation Officer.\n", "favicon": "favicon"}, "curation": {"applicationTitle": "Expeditionary Innovation", "curationMeta": {"oppTable": {"cols": [{"colId": "status", "colWidth": 115, "pinned": false, "hidden": false}, {"colId": "priority", "colWidth": 125, "pinned": false, "hidden": false}, {"colId": "title", "colWidth": 250, "pinned": false, "hidden": false}, {"colId": "function", "colWidth": 225, "pinned": false, "hidden": false}, {"colId": "relatedOpportunityCount", "colWidth": 100, "pinned": false, "hidden": false}, {"colId": "campaign", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "lastCurated", "colWidth": 140, "pinned": false, "hidden": false}, {"colId": "createdAt", "colWidth": 110, "pinned": false, "hidden": false}, {"colId": "org1", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "stakeholders", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "armyModernizationPriority", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "capabilityArea", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "echelonApplicability", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "categories", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "transitionInContactLineOfEffort", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "operationalRoles", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "solutionPathway", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "statusNotes", "colWidth": 350, "pinned": false, "hidden": false}, {"colId": "materielSolutionType", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "visibility", "colWidth": 125, "pinned": false, "hidden": false}], "sortCols": []}}}, "fields": {"opportunity": {"campaign": {"fieldLabel": "Associated Event", "fieldDescription": "If you are submitting a problem for a specific event please select the appropriate event name in the dropdown. However, if your submission is not associated with an event, simply leave the leave the selection as “None” and proceed with your submission.", "fieldCurationDescription": "OPTIONAL: Use this field (as applicable) to associate the problem with an associated event.", "values": [{"label": "None"}, {"label": "Dragon's Lair 11"}]}, "function": {"fieldLabel": "Warfighting Function", "fieldDescription": "Select a Warfighting Function", "fieldCurationDescription": "Select a Warfighting Function. Click on the question mark for more information.", "values": [{"label": "Mission Command"}, {"label": "Movement and Maneuver"}, {"label": "Intelligence"}, {"label": "Fires"}, {"label": "Sustainment"}, {"label": "Force Protection"}]}}}, "orgValues": [{"label": "7TB(X) 7th Transportation Expidtionary Brigade"}, {"label": "10TB(T) 10th Transport Battalion"}, {"label": "11TB(T) 11th Transport Battalion"}]}