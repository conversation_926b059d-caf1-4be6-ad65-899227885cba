{"applicationTitle": "Marne Innovation Program", "company": "3ID", "division": "3rd Infantry Division", "favicon": "favicon", "landingLogo": "", "sLogo": "https://catalystserverstorage.blob.core.usgovcloudapi.net/public/marne/sLogo.png", "mLogo": "https://catalystserverstorage.blob.core.usgovcloudapi.net/public/marne/mLogo.png", "lLogo": "https://catalystserverstorage.blob.core.usgovcloudapi.net/public/marne/lLogo.png", "backdropImage": "https://catalystserverstorage.blob.core.usgovcloudapi.net/public/marne/backdropImage.jpg", "logo": "https://catalystserverstorage.blob.core.usgovcloudapi.net/public/marne/logo.png", "submission": {"applicationTitle": "Marne Innovation Program", "welcomeTitle": "3ID\nInnovation Center", "company": "3ID", "division": "3rd Infantry Division", "text1": "The Marne Innovations program identifies current capability gaps within the 3rd Infantry Division and works with Industry and Academic partners to rapidly identify, develop, and implement solutions.", "text2": "The Marne Innovations program was started in 2021 to focus on Soldier-centered innovation at Fort Stewart and Hunter Army Airfield. The program has three lines of effort that ultimately aim to solve today's problems with today's technology.\n\nFirst, to make our Soldiers more lethal on the battlefield by rapidly identifying solutions that solve current operational capability gaps.\nSecond, to give time back to the warfighter by replacing antiquated systems and processes that are a burden to our organization.\nAnd third, to create a culture of innovation by providing an outlet for interested Soldiers to learn technical skills and contribute to solution development.\n\nWe value the diverse backgrounds and unique skillsets that exist within our organization, and want to leverage them to make the 3rd Infantry Division the premier armored fighting force in the United States Army.", "text3": "Not for reporting SHARP/EO/EEO reports or Emergency Situations. Those reports should be sent through the chain of command or appropriate authorities/agencies.\n\n", "text4": "", "text5": "Your problem statement will be reviewed by your Brigade or Division Innovation Officer.\n\n", "favicon": "favicon"}, "curation": {"curationMeta": {"oppTable": {"cols": [{"colId": "status", "colWidth": 115, "pinned": false, "hidden": false}, {"colId": "priority", "colWidth": 125, "pinned": false, "hidden": false}, {"colId": "title", "colWidth": 250, "pinned": false, "hidden": false}, {"colId": "function", "colWidth": 225, "pinned": false, "hidden": false}, {"colId": "relatedOpportunityCount", "colWidth": 100, "pinned": false, "hidden": false}, {"colId": "campaign", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "lastCurated", "colWidth": 140, "pinned": false, "hidden": false}, {"colId": "createdAt", "colWidth": 110, "pinned": false, "hidden": false}, {"colId": "org1", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "stakeholders", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "armyModernizationPriority", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "capabilityArea", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "echelonApplicability", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "categories", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "transitionInContactLineOfEffort", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "operationalRoles", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "solutionPathway", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "statusNotes", "colWidth": 350, "pinned": false, "hidden": false}, {"colId": "materielSolutionType", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "visibility", "colWidth": 125, "pinned": false, "hidden": false}], "sortCols": []}}}, "fields": {"opportunity": {"campaign": {"fieldLabel": "Associated Event", "fieldDescription": "If you are submitting a problem for a specific event please select the appropriate event name in the dropdown. However, if your submission is not associated with an event, simply leave the leave the selection as “None” and proceed with your submission.", "fieldCurationDescription": "OPTIONAL: Use this field (as applicable) to associate the problem with an associated event.", "values": [{"label": "None"}, {"label": "Dragon's Lair 11"}]}, "function": {"fieldLabel": "Warfighting Function", "fieldDescription": "Select a Warfighting Function", "fieldCurationDescription": "Select a Warfighting Function. Click on the question mark for more information.", "values": [{"label": "Mission Command"}, {"label": "Movement and Maneuver"}, {"label": "Intelligence"}, {"label": "Fires"}, {"label": "Sustainment"}, {"label": "Force Protection"}]}}}, "orgValues": [{"label": "1ABCT"}, {"label": "2ABCT"}, {"label": "3CAB"}, {"label": "3DSB"}, {"label": "DIVARTY"}, {"label": "HHBN"}, {"label": "48IBCT"}, {"label": "Other Tenant Unit"}]}