{"applicationTitle": "AFC INNOVATION", "company": "AFC", "division": "AFC", "favicon": "favicon", "sLogo": "https://catalystserverstorage.blob.core.usgovcloudapi.net/public/afc/sLogo.png", "mLogo": "https://catalystserverstorage.blob.core.usgovcloudapi.net/public/afc/mLogo.png", "lLogo": "https://catalystserverstorage.blob.core.usgovcloudapi.net/public/afc/lLogo.png", "logo": "https://catalystserverstorage.blob.core.usgovcloudapi.net/public/afc/logo.png", "backdropImage": "https://catalystserverstorage.blob.core.usgovcloudapi.net/public/afc/backdropImage.png", "submission": {"applicationTitle": "AFC INNOVATION", "welcomeTitle": "AFC\nINNOVATION CENTER", "text1": "We connect people, ideas and resources that empower Soldiers to develop creative solutions.", "text2": "", "text3": "Not for reporting SHARP/EO/EEO reports or Emergency Situations. Those reports should be sent through the chain of command or appropriate authorities/agencies.\n\n", "text4": "SHARP contacts:\nAFC 24/7 SHARP Hotline: 512-914-2948", "text5": "Your problem statement will be reviewed by AFC Futures & Concepts Center.\n\n", "company": "AFC", "division": "AFC", "favicon": "favicon"}, "curation": {"curationMeta": {"oppTable": {"cols": [{"colId": "status", "colWidth": 115, "pinned": false, "hidden": false}, {"colId": "priority", "colWidth": 125, "pinned": false, "hidden": false}, {"colId": "title", "colWidth": 250, "pinned": false, "hidden": false}, {"colId": "function", "colWidth": 225, "pinned": false, "hidden": false}, {"colId": "relatedOpportunityCount", "colWidth": 100, "pinned": false, "hidden": false}, {"colId": "lastCurated", "colWidth": 140, "pinned": false, "hidden": false}, {"colId": "createdAt", "colWidth": 110, "pinned": false, "hidden": false}, {"colId": "org1", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "stakeholders", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "armyModernizationPriority", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "capabilityArea", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "echelonApplicability", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "categories", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "transitionInContactLineOfEffort", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "operationalRoles", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "solutionPathway", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "statusNotes", "colWidth": 350, "pinned": false, "hidden": false}, {"colId": "materielSolutionType", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "visibility", "colWidth": 125, "pinned": false, "hidden": false}], "sortCols": []}}}, "fields": {"opportunity": {"function": {"fieldLabel": "Warfighting Function", "fieldDescription": "Select a Warfighting Function", "fieldCurationDescription": "Select a Warfighting Function. Click on the question mark for more information.", "values": [{"label": "Mission Command"}, {"label": "Movement and Maneuver"}, {"label": "Intelligence"}, {"label": "Fires"}, {"label": "Sustainment"}, {"label": "Force Protection"}]}}}, "orgValues": [{"label": "Futures and Concepts Center (FCC)"}, {"label": "Combat Capabilities Development Command (CCDC)"}, {"label": "Cross-Functional Teams (CFTs)"}, {"label": "Other"}]}