{"applicationTitle": "2CR INNOVATION", "company": "2CR", "division": "2CR", "favicon": "favicon", "sLogo": "https://catalystserverstorage.blob.core.usgovcloudapi.net/public/2cr/sLogo.png", "mLogo": "https://catalystserverstorage.blob.core.usgovcloudapi.net/public/2cr/mLogo.png", "lLogo": "https://catalystserverstorage.blob.core.usgovcloudapi.net/public/2cr/lLogo.png", "logo": "https://catalystserverstorage.blob.core.usgovcloudapi.net/public/2cr/logo.png", "backdropImage": "https://catalystserverstorage.blob.core.usgovcloudapi.net/public/2cr/backdropImage.png", "submission": {"applicationTitle": "2CR INNOVATION", "welcomeTitle": "2d Cavalry Regiment\nINNOVATION PROGRAM", "text1": "We connect people, ideas, and resources to empower all Dragoons to develop creative solutions to solve tactical problems.", "text2": "2CR Innovation Dashboard is the central point of connection for Dragoon innovators across the Regiment to submit, monitor, and advance problem statements through the innovation process to produce results.", "text3": "Not for reporting SHARP/EO/EEO reports or Emergency Situations. Those reports should be sent through the chain of command or appropriate authorities/agencies.\n\n", "text4": "", "text5": "Your problem statement will be reviewed by you Brigade or Division Innovation Officer. If you have any questions, contact <NAME_EMAIL>.", "company": "2CR", "division": "2CR", "favicon": "favicon"}, "curation": {"curationMeta": {"oppTable": {"cols": [{"colId": "status", "colWidth": 115, "pinned": false, "hidden": false}, {"colId": "priority", "colWidth": 125, "pinned": false, "hidden": false}, {"colId": "title", "colWidth": 250, "pinned": false, "hidden": false}, {"colId": "function", "colWidth": 225, "pinned": false, "hidden": false}, {"colId": "relatedOpportunityCount", "colWidth": 100, "pinned": false, "hidden": false}, {"colId": "lastCurated", "colWidth": 140, "pinned": false, "hidden": false}, {"colId": "createdAt", "colWidth": 110, "pinned": false, "hidden": false}, {"colId": "org1", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "stakeholders", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "armyModernizationPriority", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "capabilityArea", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "echelonApplicability", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "categories", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "transitionInContactLineOfEffort", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "operationalRoles", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "solutionPathway", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "statusNotes", "colWidth": 350, "pinned": false, "hidden": false}, {"colId": "materielSolutionType", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "visibility", "colWidth": 125, "pinned": false, "hidden": false}], "sortCols": []}}}, "fields": {"opportunity": {"function": {"fieldLabel": "Warfighting Function", "fieldDescription": "Select a Warfighting Function", "fieldCurationDescription": "Select a Warfighting Function. Click on the question mark for more information.", "values": [{"label": "Mission Command"}, {"label": "Movement and Maneuver"}, {"label": "Intelligence"}, {"label": "Fires"}, {"label": "Sustainment"}, {"label": "Force Protection"}]}}}, "orgValues": [{"label": "Regiment HQs"}, {"label": "1/2"}, {"label": "2/2"}, {"label": "3/2"}, {"label": "4/2"}, {"label": "Support Squadron"}, {"label": "Field Artillery Squadron"}, {"label": "Engineer"}]}