{"applicationTitle": "Warfighter Innovation", "company": "101st Airborne", "division": "Warfighter", "favicon": "favicon", "landingLogo": "", "sLogo": "https://catalystserverstorage.blob.core.usgovcloudapi.net/public/101stairborne/sLogo.png", "mLogo": "https://catalystserverstorage.blob.core.usgovcloudapi.net/public/101stairborne/mLogo.png", "lLogo": "https://catalystserverstorage.blob.core.usgovcloudapi.net/public/101stairborne/lLogo.png", "logo": "https://catalystserverstorage.blob.core.usgovcloudapi.net/public/101stairborne/logo.png", "backdropImage": "https://catalystserverstorage.blob.core.usgovcloudapi.net/public/101stairborne/backdropImage.png", "submission": {"applicationTitle": "Warfighter Innovation", "welcomeTitle": "101st Airborne\nInnovation Center", "text1": "We connect people, ideas, and resources to empower all Paratroopers to develop creative solutions to solve tactical problems.", "text2": "Solving Warfighter problems is our mission.\n\nThe 101st Division Innovation Dashboard is the central point of connection for Warfighters and innovators across the Division to submit, monitor, and advance problem statements through the innovation process to produce results. The 101st Division Innovation Dashboard links innovators to the resources and network needed to enable warfighter-centered innovations to solve Warfighter problems.\n\nHow We Can Help You and Your Unit:\n\n- Connect you to available resources, SMEs, academic and research partners, and information to help you develop innovative solutions\n- Offer opportunities to partake in specialized training to advance innovation efforts across the Division\n- Advocate for additional support\n", "text3": "Not for reporting SHARP/EO/EEO reports or Emergency Situations. Those reports should be sent through the chain of command or appropriate authorities/agencies.\n\n", "text4": "SHARP contacts:\nSFC Tebay Jeremiah\n<EMAIL>\n\nSFC <PERSON><PERSON><PERSON>-Davis\n<EMAIL>\n\nEO/EEO Contact:\nEO Advisor: MSG <PERSON>\n<EMAIL>\n\nEO Hotline: (270) 472-5675", "text5": "Your problem statement will be reviewed by your Brigade or Division Innovation Officer.\n\n", "company": "101st Airborne", "division": "Warfighter", "favicon": "favicon"}, "curation": {"curationMeta": {"oppTable": {"cols": [{"colId": "status", "colWidth": 115, "pinned": false, "hidden": false}, {"colId": "priority", "colWidth": 125, "pinned": false, "hidden": false}, {"colId": "title", "colWidth": 250, "pinned": false, "hidden": false}, {"colId": "function", "colWidth": 225, "pinned": false, "hidden": false}, {"colId": "relatedOpportunityCount", "colWidth": 100, "pinned": false, "hidden": false}, {"colId": "campaign", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "lastCurated", "colWidth": 140, "pinned": false, "hidden": false}, {"colId": "createdAt", "colWidth": 110, "pinned": false, "hidden": false}, {"colId": "org1", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "stakeholders", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "armyModernizationPriority", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "capabilityArea", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "echelonApplicability", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "categories", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "transitionInContactLineOfEffort", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "operationalRoles", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "solutionPathway", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "statusNotes", "colWidth": 350, "pinned": false, "hidden": false}, {"colId": "materielSolutionType", "colWidth": 200, "pinned": false, "hidden": false}, {"colId": "visibility", "colWidth": 125, "pinned": false, "hidden": false}], "sortCols": []}}}, "fields": {"opportunity": {"campaign": {"fieldLabel": "Associated Event", "fieldDescription": "If you are submitting a problem for a specific event please select the appropriate event name in the dropdown. However, if your submission is not associated with an event, simply leave the leave the selection as “None” and proceed with your submission.", "fieldCurationDescription": "OPTIONAL: Use this field (as applicable) to associate the problem with an associated event.", "values": [{"label": "None"}, {"label": "Dragon's Lair 11"}]}, "function": {"fieldLabel": "Warfighting Function", "fieldDescription": "Select a Warfighting Function", "fieldCurationDescription": "Select a Warfighting Function. Click on the question mark for more information.", "values": [{"label": "Mission Command"}, {"label": "Movement and Maneuver"}, {"label": "Intelligence"}, {"label": "Fires"}, {"label": "Sustainment"}, {"label": "Force Protection"}]}}}, "orgValues": [{"label": "101 CAB"}, {"label": "101 DSB"}, {"label": "101 HHBn"}, {"label": "1BCT", "children": [{"label": "1-327 IN"}, {"label": "1-506 IN"}, {"label": "2-327 IN"}, {"label": "HHC"}, {"label": "MFRC"}]}, {"label": "21 DSIG"}, {"label": "2-44 ADA"}, {"label": "2BCT", "children": [{"label": "1-26 IN"}, {"label": "1-502 IN"}, {"label": "2-502 IN"}, {"label": "HHC"}, {"label": "MFRC"}]}, {"label": "3BCT", "children": [{"label": "1-187 IN"}, {"label": "2-506 IN"}, {"label": "3-187 IN"}, {"label": "HHC"}, {"label": "MFRC"}]}, {"label": "302 IEW"}, {"label": "326 DEB"}, {"label": "52nd EOD"}, {"label": "531st Hospital Command"}, {"label": "AASLT School"}, {"label": "CAB", "children": [{"label": "2-17 AVN"}, {"label": "1-101 AVN"}, {"label": "5-101 AVN"}, {"label": "6-101 AVN"}, {"label": "96 ASB"}, {"label": "HHC BDE"}]}, {"label": "DIVARTY", "children": [{"label": "1-320 FA"}, {"label": "2-32 FA"}, {"label": "2-44 ADA"}, {"label": "3-320 FA"}, {"label": "HHBN"}, {"label": "HHB BDE"}, {"label": "HHC BDE"}]}, {"label": "DSB", "children": [{"label": "101 STB"}, {"label": "129 CSSB"}, {"label": "426 LSB"}, {"label": "526 LSB"}, {"label": "626 LSB"}, {"label": "716 MP"}]}, {"label": "H2F"}, {"label": "<PERSON><PERSON><PERSON>"}, {"label": "Other"}]}