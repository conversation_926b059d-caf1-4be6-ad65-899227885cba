# Audit Logs Configuration

This directory contains the configuration for the system's audit logging functionality. The `audit-logs.json` file defines which events should be tracked and logged in the system.

## Purpose

The audit logs configuration serves to:

1. Define which entity changes should be tracked
2. Specify what type of events should be logged for each entity
3. Configure which fields should trigger logging for update operations
4. Define conditional logic for when events should be logged based on field value changes

## File Structure

The `audit-logs.json` file is organized by entity type, with each entity having an array of event configurations. Each event configuration has the following properties:

- `eventType`: A unique identifier for the type of event (e.g., "user.creation")
- `entityType`: The type of entity this event applies to
- `op`: The operation type that triggers the event ("create", "update", or "delete")
- `fields`: (Optional) For update operations, specifies which fields should trigger the event
- `conditionGroups`: (Optional) For update operations, defines conditional logic that must be satisfied for the event to be logged

## Condition Groups

Condition groups allow you to define complex conditional logic for when audit events should be logged. This is particularly useful for tracking specific state transitions or value changes.

### Condition Group Structure

Each condition group contains:

- `operator`: Either "and" or "or" to define how conditions within the group are evaluated
- `conditions`: An array of individual conditions

### Individual Conditions

Each condition specifies:

- `field`: The field name to check
- `from`: (Optional) The previous value the field should have
- `to`: (Optional) The new value the field should have
- `operator`: The comparison operator (currently supports "eq" for equality)

### Evaluation Logic

- If multiple condition groups exist, any group that evaluates to `true` will trigger the event
- Within each group, conditions are combined using the group's operator ("and" or "or")
- If `from` is not specified, any previous value will match
- If `to` is not specified, any new value will match
- Both `from` and `to` must match for the condition to be true

## Important Notes

1. **Server Restart Required**: Changes to this configuration file require a server restart to take effect. The configuration is loaded at server startup.

2. **ChangeEventType Enum Synchronization**: The `eventType` values in this configuration must match the values defined in the `ChangeEventType` enum (`src/core/contracts/enums/ChangeEventType.ts`). This is because:
   - The `ChangeEvent` entity uses this enum for its `eventType` field
   - The system validates events against this enum
   - Any mismatch will cause the event tracking to fail

## Example Configuration

```json
{
  "User": [
    {
      "eventType": "user.creation",
      "op": "create",
      "entityType": "User"
    },
    {
      "eventType": "user.passwordChange",
      "op": "update",
      "fields": ["_password"],
      "entityType": "User"
    },
    {
      "eventType": "user.lock",
      "op": "update",
      "fields": ["status"],
      "entityType": "User",
      "conditionGroups": [
        {
          "operator": "or",
          "conditions": [
            {
              "field": "status",
              "from": "VERIFIED",
              "to": "LOCKED",
              "operator": "eq"
            },
            {
              "field": "status",
              "from": "UNVERIFIED",
              "to": "LOCKED",
              "operator": "eq"
            }
          ]
        }
      ]
    }
  ]
}
```

## Related Components

- `ChangeEvent` entity: Stores the actual audit log entries
- `ChangeEventSubscriber`: Handles the creation of audit log entries
- `ChangeEventType` enum (`src/core/contracts/enums/ChangeEventType.ts`): Defines the valid event types that can be tracked
