import { Connection, EntityManager, IDatabaseDriver } from '@mikro-orm/core';
import Application from 'application';
import { getToken } from 'core/auth/authUtils';
import { expect } from 'chai';
import { errorCodes, errorKeys } from 'core/contracts/errors/ErrorCodes';
import { ID_PREFIX_0,  restoreDbFromLocalBackup } from 'loadFixtures';
import supertest, { SuperTest, Test } from 'supertest';
import { Id } from 'core/utils/Id';
import { PayloadKeys } from 'core/auth/JwtPayload';
import { RoleNames, roleMap } from 'core/contracts/enums/RoleNames';
import { Stakeholder } from 'core/entities/Stakeholder';

let request: SuperTest<Test>;
let application: Application;
let em: EntityManager<IDatabaseDriver<Connection>> | undefined = undefined;
let curatorToken: string;

describe('))))))))))))) Curator Stakeholder Tests ((((((((((((((', async () => {
  before(async () => {
    application = new Application();
    await application.init();
    await application.start();
    em = application.orm?.em.fork();
    request = supertest(application.app);
    // user <EMAIL>
    // this is the prefix for monumentsMen (see loadFixtures.ts)
    curatorToken = getToken({
      [PayloadKeys.USER_KEY]: Id.simpleId(3, ID_PREFIX_0),
      [PayloadKeys.TENANT_KEY]: Id.simpleId(1),
      [PayloadKeys.ROLES_KEY]: [roleMap[RoleNames.CURATOR]],
    }).token;
    restoreDbFromLocalBackup();
    console.log('🚀 Database cleared, fixtures loaded');
  });

  // beforeEach(async () => {
  //   restoreDbFromLocalBackup();
  //   console.log('🚀 Database cleared, fixtures loaded');
  // });

  after(async () => {
    application.server?.close();
  });

  it('get stakeholders alphabetical', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `query {
        queryStakeholders(
          pagingInput: { pageSize: 5, cursor: "5" },
          searchSortInput: {
            sortFields: [{ fieldName: "id", ascending: true }]
          }
        ) {
          results {
           id name firstName lastName org organizationRole
          }
        }
      }
      `,
      })
      .expect(200);
    expect(response.body.data.queryStakeholders.results).to.be.a('array');
    expect(response.body.data.queryStakeholders.results.length).equal(5);
    expect(response.body.data.queryStakeholders.results[0].name).equal('stakeholder_6');
    expect(response.body.data.queryStakeholders.results[0].firstName).equal('stakeholder_6');
    expect(response.body.data.queryStakeholders.results[0].lastName).to.be.null;
    expect(response.body.data.queryStakeholders.results[0].org).equal('stakeholder_org_6');
    expect(response.body.data.queryStakeholders.results[0].organizationRole).to.be.null;
  });

  it('search stakeholders by prefix, sort alphabetical', async () => {
    // this should match stakeholder_1 and stakeholder_10
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `query {
        queryStakeholders(
          pagingInput: { pageSize: 10 },
          searchSortInput: {
            sortFields: [{ fieldName: "name", ascending: true }],
            searchFields: [{ fieldNames: ["name"], operator: MATCH, searchValue: "stakeholder_1" }]
          }
        ) {
          results {
           id name firstName lastName org
          }
        }
      }
      `,
      })
      .expect(200);
    expect(response.body.data.queryStakeholders.results).to.be.a('array');
    expect(response.body.data.queryStakeholders.results.length).equal(2);
    expect(response.body.data.queryStakeholders.results[0].name).equal('stakeholder_1');
    expect(response.body.data.queryStakeholders.results[0].firstName).equal('stakeholder_1');
    expect(response.body.data.queryStakeholders.results[0].lastName).to.be.null;
    expect(response.body.data.queryStakeholders.results[1].name).equal('stakeholder_10');
    expect(response.body.data.queryStakeholders.results[1].firstName).equal('stakeholder_10');
    expect(response.body.data.queryStakeholders.results[1].lastName).to.be.null;
  });

  it('search stakeholders by prefix, sort alphabetical', async () => {
    // this should not match stakeholder_1 and stakeholder_10
    // stakeholder_10 has null org
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `query {
        queryStakeholders(
          pagingInput: { pageSize: 10 },
          searchSortInput: {
            sortFields: [{ fieldName: "name", ascending: true }],
            searchFields: [
              { fieldNames: ["name"], operator: MATCH, searchValue: "stakeholder_1" },
              { fieldNames: ["org"], operator: MATCH, searchValue: "stakeholder_org_1" },
            ]
          }
        ) {
          results {
           id name firstName lastName
          }
        }
      }
      `,
      })
      .expect(200);
    expect(response.body.data.queryStakeholders.results).to.be.a('array');
    expect(response.body.data.queryStakeholders.results.length).equal(1);
    expect(response.body.data.queryStakeholders.results[0].name).equal('stakeholder_1');
    expect(response.body.data.queryStakeholders.results[0].firstName).equal('stakeholder_1');
    expect(response.body.data.queryStakeholders.results[0].lastName).to.be.null;
  });

  it('should get stakeholder by name and org', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `query {
          queryStakeholders(
            searchSortInput: {
              searchFields: [
                { fieldNames: ["firstName"], operator: EQ, searchValue: "stakeholder_5" },
                { fieldNames: ["org"], operator: EQ, searchValue: "stakeholder_org_5" },
              ]
            }
          ) {
            results {
              id name org firstName lastName
            }
          }
        }
        `,
      })
      .expect(200);
    
    const stakeholderResults = response.body.data.queryStakeholders.results;
    expect(stakeholderResults).to.be.a('array');
    expect(stakeholderResults.length).equal(1);
    expect(stakeholderResults[0].id).equal(Id.simpleId(5, ID_PREFIX_0));
    expect(stakeholderResults[0].name).equal('stakeholder_5');
    expect(stakeholderResults[0].firstName).equal('stakeholder_5');
    expect(stakeholderResults[0].lastName).to.be.null;
    expect(stakeholderResults[0].org).equal('stakeholder_org_5');
  });

  it('should get stakeholder by name and org w/ null org', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `query {
            queryStakeholders(
              searchSortInput: {
                searchFields: [
                  { fieldNames: ["firstName"], operator: EQ, searchValue: "stakeholder_10" },
                ]
              }
            ) {
              results {
                id name org firstName lastName
              }
            }
          }
        `,
      })
      .expect(200);

    const stakeholderResults = response.body.data.queryStakeholders.results;
    expect(stakeholderResults).to.be.a('array');
    expect(stakeholderResults.length).equal(1);
    expect(stakeholderResults[0].id).equal(Id.simpleId(10, ID_PREFIX_0));
    expect(stakeholderResults[0].name).equal('stakeholder_10');
    expect(stakeholderResults[0].firstName).equal('stakeholder_10');
    expect(stakeholderResults[0].lastName).to.be.null;
    expect(stakeholderResults[0].org).to.be.null;
  });

  it('should get stakeholder by id', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `query {
          getStakeholder(id: "${Id.simpleId(3, ID_PREFIX_0)}") {
            id name firstName lastName
          }
        }
        `,
      })
      .expect(200);

    expect(response.body.data.getStakeholder).to.be.a('object');
    expect(response.body.data.getStakeholder.id).equal(Id.simpleId(3, ID_PREFIX_0));
    expect(response.body.data.getStakeholder.name).equal('stakeholder_3');
    expect(response.body.data.getStakeholder.firstName).equal('stakeholder_3');
    expect(response.body.data.getStakeholder.lastName).to.be.null;
  });

  it('should create stakeholder', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `mutation {
          createStakeholder (
            input: {
              firstName: "test_stakeholder"
              org: "test_stakeholder_org"
            },
          ){
            id name org lastName firstName
          }
        }
        `,
      })
      .expect(200);

    expect(response.body.data.createStakeholder).to.be.a('object');
    expect(response.body.data.createStakeholder.id).to.not.be.null;
    expect(response.body.data.createStakeholder.name).equal('test_stakeholder');
    expect(response.body.data.createStakeholder.firstName).equal('test_stakeholder');
    expect(response.body.data.createStakeholder.lastName).to.be.null;
    expect(response.body.data.createStakeholder.org).equal('test_stakeholder_org');
  });

  it('should return existing stakeholder if already exists (not case sensitive)', async () => {
    restoreDbFromLocalBackup();
    console.log('🚀 Database cleared, fixtures loaded');
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `mutation {
          createStakeholder (
            input: {
              firstName: "StakEhoLdeR_1"
              org: "StakEhoLdeR_org_1"
            },
          ){
            id name org lastName firstName
          }
        }
        `,
      })
      .expect(200);

    expect(response.body.data.createStakeholder).to.be.a('object');
    expect(response.body.data.createStakeholder.id).to.not.be.null;
    expect(response.body.data.createStakeholder.name).equal('StakEhoLdeR_1');
    expect(response.body.data.createStakeholder.firstName).equal('StakEhoLdeR_1');
    expect(response.body.data.createStakeholder.lastName).to.be.null;
    expect(response.body.data.createStakeholder.org).equal('StakEhoLdeR_org_1');
  });

  it('should return existing stakeholder if already exists (no org) (not case sensitive)', async () => {
    restoreDbFromLocalBackup();
    console.log('🚀 Database cleared, fixtures loaded');
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `mutation {
          createStakeholder (
            input: {
              firstName: "StakEhoLdeR_10"
            },
          ){
            id name org lastName firstName
          }
        }
        `,
      })
      .expect(200);

    expect(response.body.data.createStakeholder).to.be.a('object');
    expect(response.body.data.createStakeholder.id).to.not.be.null;
    expect(response.body.data.createStakeholder.name).equal('StakEhoLdeR_10');
    expect(response.body.data.createStakeholder.firstName).equal('StakEhoLdeR_10');
    expect(response.body.data.createStakeholder.lastName).to.be.null;
    expect(response.body.data.createStakeholder.org).null;
  });

  it('should succeed creating stakeholder with duplicate name but not org', async () => {
    restoreDbFromLocalBackup();
    console.log('🚀 Database cleared, fixtures loaded');
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `mutation {
          createStakeholder (
            input: {
              firstName: "stakeholder_1"
            },
          ){
            id name org lastName firstName
          }
        }
        `,
      })
      .expect(200);

    expect(response.body.data.createStakeholder).to.be.a('object');
    expect(response.body.data.createStakeholder.id).to.not.be.null;
    expect(response.body.data.createStakeholder.name).equal('stakeholder_1');
    expect(response.body.data.createStakeholder.firstName).equal('stakeholder_1');
    expect(response.body.data.createStakeholder.lastName).to.be.null;
    expect(response.body.data.createStakeholder.org).equal(null);
  });

  it('should succeed creating stakeholder using firstName lastName and ignoring name', async () => {
    restoreDbFromLocalBackup();
    console.log('🚀 Database cleared, fixtures loaded');
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `mutation {
          createStakeholder (
            input: {
              firstName: "stakeholder"
              lastName: "MeLast"
            },
          ){
            id name org lastName firstName
          }
        }
        `,
      })
      .expect(200);

    expect(response.body.data.createStakeholder).to.be.a('object');
    expect(response.body.data.createStakeholder.id).to.not.be.null;
    expect(response.body.data.createStakeholder.name).equal('stakeholder MeLast');
    expect(response.body.data.createStakeholder.firstName).equal('stakeholder');
    expect(response.body.data.createStakeholder.lastName).equal('MeLast');
    expect(response.body.data.createStakeholder.org).to.be.null;
  });

  it('should allow duplicate entry of the same data.', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `mutation {
          createStakeholder (
            input: {
              firstName: "stakeholder"
              lastName: "MeLast"
            },
          ){
            id name org lastName firstName
          }
        }
        `,
      })
      .expect(200);

    expect(response.body.data.createStakeholder).to.be.a('object');
    expect(response.body.data.createStakeholder.id).to.not.be.null;
    expect(response.body.data.createStakeholder.name).equal('stakeholder MeLast');
    expect(response.body.data.createStakeholder.firstName).equal('stakeholder');
    expect(response.body.data.createStakeholder.lastName).equal('MeLast');
    expect(response.body.data.createStakeholder.org).to.be.null;
  });

  it('Check previous entry was duplicated', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `query {
            queryStakeholders(
              searchSortInput: {
                searchFields: [
                  { fieldNames: ["firstName"], operator: EQ, searchValue: "stakeholder" },
                   { fieldNames: ["lastName"], operator: EQ, searchValue: "MeLast" },
                ]
              }
            ) {
              results {
                id name org firstName lastName
              }
            }
          }
        `,
      })
      .expect(200);

    const stakeholderResults = response.body.data.queryStakeholders.results;
    expect(stakeholderResults).to.be.a('array');
    expect(stakeholderResults.length).equal(2);
    expect(stakeholderResults.map((stakeholder: Stakeholder) => stakeholder.firstName)).to.include('stakeholder');
    expect(stakeholderResults.map((stakeholder: Stakeholder) => stakeholder.lastName)).to.include('MeLast');
  });

  it('should fail due to empty values being passed', async () => {
    restoreDbFromLocalBackup();
    console.log('🚀 Database cleared, fixtures loaded');
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `mutation {
          createStakeholder (
            input: {
              firstName: ""
              lastName: ""
            },
          ){
            id name org lastName firstName
          }
        }
        `,
      })
      .expect(400);

    expect(response.body.errors[0].code).equal(errorCodes[errorKeys.ARGUMENT_VALIDATION_ERROR].code);
  });

  it('should update stakeholder', async () => {
    restoreDbFromLocalBackup();
    console.log('🚀 Database cleared, fixtures loaded');
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `mutation {
        updateStakeholder (input: {
          firstName: "renamed_stakeholder_3",
          org: "renamed_stakeholder_3_org",
        }, id: "${Id.simpleId(3, ID_PREFIX_0)}") {
          id name org lastName firstName
        }
      }
      `,
      })
      .expect(200);

    expect(response.body.data.updateStakeholder).to.be.a('object');
    expect(response.body.data.updateStakeholder.id).equal(Id.simpleId(3, ID_PREFIX_0));
    expect(response.body.data.updateStakeholder.name).equal('renamed_stakeholder_3');
    expect(response.body.data.updateStakeholder.firstName).equal('renamed_stakeholder_3');
    expect(response.body.data.updateStakeholder.lastName).to.be.null;
    expect(response.body.data.updateStakeholder.org).equal('renamed_stakeholder_3_org');
  });

  it('should not delete stakeholder that is still referenced by a projectStakeholder', async () => {
    restoreDbFromLocalBackup();
    console.log('🚀 Database cleared, fixtures loaded');
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `mutation {
          deleteStakeholder (id: "${Id.simpleId(3, ID_PREFIX_0)}")
        }
        `,
      })
      .expect(200);

    expect(response.body.data.deleteStakeholder).to.be.false;
  });
});
