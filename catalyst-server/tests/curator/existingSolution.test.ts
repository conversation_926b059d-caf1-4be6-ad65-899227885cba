import { Connection, EntityManager, IDatabaseDriver } from '@mikro-orm/core';
import Application from 'application';
import { getToken } from 'core/auth/authUtils';
import { expect } from 'chai';
import { ID_PREFIX_0, restoreDbFromLocalBackup } from 'loadFixtures';
import supertest, { SuperTest, Test } from 'supertest';
import { Id } from 'core/utils/Id';
import { PayloadKeys } from 'core/auth/JwtPayload';
import { RoleNames, roleMap } from 'core/contracts/enums/RoleNames';

let request: SuperTest<Test>;
let application: Application;
let em: EntityManager<IDatabaseDriver<Connection>> | undefined = undefined;
let curatorToken: string;

describe('))))))))))))) Curator ExistingSolution Tests ((((((((((((((', async () => {
  before(async () => {
    application = new Application();
    await application.init();
    await application.start();
    em = application.orm?.em.fork();
    request = supertest(application.app);
    // user <EMAIL>
    // this is the prefix for monumentsMen (see loadFixtures.ts)
    curatorToken = getToken({
      [PayloadKeys.USER_KEY]: Id.simpleId(3, ID_PREFIX_0),
      [PayloadKeys.TENANT_KEY]: Id.simpleId(1),
      [PayloadKeys.ROLES_KEY]: [roleMap[RoleNames.CURATOR]],
    }).token;
  });

  beforeEach(async () => {
    restoreDbFromLocalBackup();
    console.log('🚀 Database cleared, fixtures loaded');
  });

  after(async () => {
    application.server?.close();
  });

  it('should create, update, and delete an existing solution', async () => {
    // First create an existing solution
    let response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `mutation {
          createExistingSolution(
            input: {
              source: "test-source.com",
              title: "Test Existing Solution",
              organization: "Test Organization",
              needsModification: true
            },
            links: {
              opportunityId: "${Id.simpleId(3, ID_PREFIX_0)}"
            }
          ) {
            id
            source
            title
            organization
            needsModification
            opportunity {
              id
            }
          }
        }`,
      })
      .expect(200);

    expect(response.body.data.createExistingSolution.id).to.be.not.null;
    expect(response.body.data.createExistingSolution.source).equal('test-source.com');
    expect(response.body.data.createExistingSolution.title).equal('Test Existing Solution');
    expect(response.body.data.createExistingSolution.organization).equal('Test Organization');
    expect(response.body.data.createExistingSolution.needsModification).equal(true);
    expect(response.body.data.createExistingSolution.opportunity.id).equal(Id.simpleId(3, ID_PREFIX_0));

    const existingSolutionId = response.body.data.createExistingSolution.id;

    // Now update the existing solution
    response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `mutation {
          updateExistingSolution(
            id: "${existingSolutionId}",
            input: {
              source: "updated-source.com",
              title: "Updated Existing Solution",
              organization: "Updated Organization",
              needsModification: false
            }
          ) {
            id
            source
            title
            organization
            needsModification
          }
        }`,
      })
      .expect(200);

    expect(response.body.data.updateExistingSolution.id).equal(existingSolutionId);
    expect(response.body.data.updateExistingSolution.source).equal('updated-source.com');
    expect(response.body.data.updateExistingSolution.title).equal('Updated Existing Solution');
    expect(response.body.data.updateExistingSolution.organization).equal('Updated Organization');
    expect(response.body.data.updateExistingSolution.needsModification).equal(false);

    // Get the existing solution by ID
    response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `query {
          getExistingSolution(id: "${existingSolutionId}") {
            id
            source
            title
            organization
            needsModification
            opportunity {
              id
            }
          }
        }`,
      })
      .expect(200);

    expect(response.body.data.getExistingSolution.id).equal(existingSolutionId);
    expect(response.body.data.getExistingSolution.source).equal('updated-source.com');
    expect(response.body.data.getExistingSolution.title).equal('Updated Existing Solution');

    // Delete the existing solution
    response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `mutation {
          deleteExistingSolution(id: "${existingSolutionId}")
        }`,
      })
      .expect(200);

    expect(response.body.data.deleteExistingSolution).equal(true);

    // Verify it's deleted by trying to get it
    response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `query {
          getExistingSolution(id: "${existingSolutionId}") {
            id
          }
        }`,
      })
      .expect(200);

    expect(response.body.data.getExistingSolution).to.be.null;
  });

  it('should get existing solutions by opportunity', async () => {
    // Get existing solutions for an opportunity that should have some from fixtures
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `query {
          getExistingSolutionsByOpportunity(opportunityId: "${Id.simpleId(1, ID_PREFIX_0)}") {
            id
            source
            title
            organization
            needsModification
          }
        }`,
      })
      .expect(200);

    expect(response.body.data.getExistingSolutionsByOpportunity).to.be.an('array');
    // The fixtures should create 1-3 existing solutions per opportunity
    expect(response.body.data.getExistingSolutionsByOpportunity.length).to.be.greaterThan(0);

    // Check that each existing solution has the required fields
    response.body.data.getExistingSolutionsByOpportunity.forEach((solution: any) => {
      expect(solution.id).to.be.not.null;
      expect(solution.source).to.be.not.null;
      expect(solution.needsModification).to.be.a('boolean');
    });
  });

  it('should create existing solution with minimal required fields', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `mutation {
          createExistingSolution(
            input: {
              source: "minimal-source.com"
            },
            links: {
              opportunityId: "${Id.simpleId(3, ID_PREFIX_0)}"
            }
          ) {
            id
            source
            title
            organization
            needsModification
          }
        }`,
      })
      .expect(200);

    expect(response.body.data.createExistingSolution.id).to.be.not.null;
    expect(response.body.data.createExistingSolution.source).equal('minimal-source.com');
    expect(response.body.data.createExistingSolution.title).to.be.null;
    expect(response.body.data.createExistingSolution.organization).to.be.null;
    expect(response.body.data.createExistingSolution.needsModification).equal(false); // default value
  });

  it('should fail to create existing solution with invalid opportunity', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `mutation {
          createExistingSolution(
            input: {
              source: "test-source.com"
            },
            links: {
              opportunityId: "00000000-0000-0000-0000-999999999999"
            }
          ) {
            id
          }
        }`,
      })
      .expect(400);

    expect(response.body.errors).to.be.not.null;
    expect(response.body.errors[0].message).to.contain('This item was not found');
  });

  it('should fail to update non-existent existing solution', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `mutation {
          updateExistingSolution(
            id: "00000000-0000-0000-0000-999999999999",
            input: {
              source: "updated-source.com"
            }
          ) {
            id
          }
        }`,
      })
      .expect(400);

    expect(response.body.errors).to.be.not.null;
    expect(response.body.errors[0].message).to.contain('This item was not found');
  });
});
