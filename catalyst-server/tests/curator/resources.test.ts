import { Connection, EntityManager, IDatabaseDriver } from '@mikro-orm/core';
import Application from 'application';
import { getToken } from 'core/auth/authUtils';
import { expect } from 'chai';
import { ID_PREFIX_0,  MATCH_SUBSTRING, OPS_PER_TENANT, restoreDbFromLocalBackup } from 'loadFixtures';
import supertest, { SuperTest, Test } from 'supertest';
import { Id } from 'core/utils/Id';
import { PayloadKeys } from 'core/auth/JwtPayload';
import { RoleNames, roleMap } from 'core/contracts/enums/RoleNames';
import { errorCodes, errorKeys } from 'core/contracts/errors/ErrorCodes';
import { Opportunity } from 'core/entities/Opportunity';

let request: SuperTest<Test>;
let application: Application;
let em: EntityManager<IDatabaseDriver<Connection>> | undefined = undefined;
let curatorToken: string;
let analystToken: string;

describe(')))))))))))))) Curator Opportunity Tests ((((((((((((((', async () => {
  before(async () => {
    application = new Application();
    await application.init();
    await application.start();
    em = application.orm?.em.fork();
    request = supertest(application.app);
    // user <EMAIL>
    // this is the prefix for users for monumentsMen (see loadFixtures.ts)
    curatorToken = getToken({
      [PayloadKeys.USER_KEY]: Id.simpleId(3, ID_PREFIX_0),
      [PayloadKeys.TENANT_KEY]: Id.simpleId(1),
      [PayloadKeys.ROLES_KEY]: [roleMap[RoleNames.CURATOR]],
    }).token;
    analystToken = getToken({
      [PayloadKeys.USER_KEY]: Id.simpleId(5, ID_PREFIX_0),
      [PayloadKeys.TENANT_KEY]: Id.simpleId(1),
      [PayloadKeys.ROLES_KEY]: [roleMap[RoleNames.CURATOR], roleMap[RoleNames.ANALYST]],
    }).token;
    restoreDbFromLocalBackup();
    console.log('🚀 Database cleared, fixtures loaded');
  });

  // beforeEach(async () => {
  //   restoreDbFromLocalBackup();
  //   console.log('🚀 Database cleared, fixtures loaded');
  // });

  after(async () => {
    application.server?.close();
  });

  it('get opportunities with no scope (for owning tenant)', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `query {
        getOpportunities {
          id title statement context benefits status
          tenant { name handle }
        }
      }
      `,
      })
      .expect(200);
    expect(response.body.data.getOpportunities).to.be.a('array');
    expect(response.body.data.getOpportunities.length).equal(OPS_PER_TENANT);
  });

  // Curator should be able to get opportunities for their tenant
  it('get opportunities w/ scope for owning tenant', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `query {
        getOpportunities(
            scope: { resources: [{ resourceId: "${Id.simpleId(1, ID_PREFIX_0)}", resourceType: TENANT }] }
        ) {
          id title statement context benefits status
          tenant { name handle }
        }
      }
      `,
      })
      .expect(200);
    expect(response.body.data.getOpportunities).to.be.a('array');
    expect(response.body.data.getOpportunities.length).equal(OPS_PER_TENANT);
  });

  // Curator should not be able to get opportunities for another tenant
  it('get opportunities w/ scope for other tenant should fail', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `query {
        getOpportunities(
            scope: { resources: [{ resourceId: "${Id.simpleId(2, ID_PREFIX_0)}", resourceType: TENANT }] }
        ) {
          id title statement context benefits status
          tenant { name handle }
        }
      }
      `,
      })
      .expect(400);
    expect(response.body.errors[0].code).equal(errorCodes[errorKeys.UNAUTHORIZED].code);
  });

  // Super should be able to get opportunities for their permitted tenants
  it('get opportunities w/ scope for for permitted tenants', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${analystToken}`)
      .send({
        query: `query {
        getOpportunities(
            scope: { resources: [{ resourceId: "${Id.simpleId(1, ID_PREFIX_0)}", resourceType: TENANT }] }
        ) {
          id title statement context benefits status
          tenant { name handle }
        }
      }
      `,
      })
      .expect(200);
    expect(response.body.data.getOpportunities).to.be.a('array');
    expect(response.body.data.getOpportunities.length).equal(OPS_PER_TENANT);
  });

  // Super should be able to get opportunities for one of their permitted tenants
  it('get opportunities w/ scope for for a permitted tenant', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${analystToken}`)
      .send({
        query: `query {
        getOpportunities(
            scope: { resources: [{ resourceId: "${Id.simpleId(1, ID_PREFIX_0)}", resourceType: TENANT }] }
        ) {
          id title statement context benefits status
          tenant { name handle }
        }
      }
      `,
      })
      .expect(200);
    expect(response.body.data.getOpportunities).to.be.a('array');
    expect(response.body.data.getOpportunities.length).equal(OPS_PER_TENANT);
  });

  // Super should be able to get opportunities for all of their permitted tenants
  it('get opportunities w/ scope for for permitted tenants', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${analystToken}`)
      .send({
        query: `query {
        getOpportunities(
            scope: { resources:
               [{ resourceId: "${Id.simpleId(1, ID_PREFIX_0)}", resourceType: TENANT },
                { resourceId: "${Id.simpleId(2, ID_PREFIX_0)}", resourceType: TENANT }]
            }
        ) {
          id title statement context benefits status
          tenant { name handle }
        }
      }
      `,
      })
      .expect(200);
    expect(response.body.data.getOpportunities).to.be.a('array');
    expect(response.body.data.getOpportunities.length).equal(OPS_PER_TENANT * 2);
    expect(response.body.data.getOpportunities.map((o: Opportunity) => o.tenant?.handle)).to.include.members([
      'monumentsMen',
      'starshipTroopers',
    ]);
  });

  // curator should not be able search all tenants
  it('curator should not be able to search opportunities for all tenants', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `query {
        queryOpportunities(
          scope: { resources:
              [{ resourceId: "${Id.simpleId(1, ID_PREFIX_0)}", resourceType: TENANT },
              { resourceId: "${Id.simpleId(2, ID_PREFIX_0)}", resourceType: TENANT }]
          },
          searchSortInput: {
            searchFields: [{ fieldNames: ["title", "statement"], operator: MATCH, searchValue: "${MATCH_SUBSTRING}"}]
          }
        ) {
          results {          
            id title statement context benefits status initiatives endorsements
            curationInfo {
              lastCurated
            }
            user {
              id emailAddress
            }
          }
        }
      }
      `,
      })
      .expect(400);
    expect(response.body.errors[0].code).equal(errorCodes[errorKeys.UNAUTHORIZED].code);
  });

  // super should be able to search all tenants
  it('super should be able to search opportunities for all tenants', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${analystToken}`)
      .send({
        query: `query {
        queryOpportunities(
          scope: { resources:
              [{ resourceId: "${Id.simpleId(1, ID_PREFIX_0)}", resourceType: TENANT },
              { resourceId: "${Id.simpleId(2, ID_PREFIX_0)}", resourceType: TENANT }]
          },
          searchSortInput: {
            searchFields: [{ fieldNames: ["title", "statement"], operator: MATCH, searchValue: "${MATCH_SUBSTRING}"}]
          }
        ) {
          results {          
            id title statement context benefits status initiatives endorsements
            curationInfo {
              lastCurated
            }
            user {
              id emailAddress
            }
          }
        }
      }
      `,
      })
      .expect(200);
    expect(response.body.data.queryOpportunities.results.length).equal(8);
  });

  // Present but empty scope should return empty results
  it('get opportunities w/ present but empty scope', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${analystToken}`)
      .send({
        query: `query {
        getOpportunities(
            scope: { resources: [] }
        ) {
          id title statement context benefits status
          tenant { name handle }
        }
      }
      `,
      })
      .expect(200);
    expect(response.body.data.getOpportunities).to.be.a('array');
    expect(response.body.data.getOpportunities.length).equal(0);
  });


});
