import { Connection, EntityManager, IDatabaseDriver } from '@mikro-orm/core';
import Application from 'application';
import { getToken } from 'core/auth/authUtils';
import { expect } from 'chai';
import { ID_PREFIX_0, restoreDbFromLocalBackup } from 'loadFixtures';
import supertest, { SuperTest, Test } from 'supertest';
import { Id } from 'core/utils/Id';
import { PayloadKeys } from 'core/auth/JwtPayload';
import { RoleNames, roleMap } from 'core/contracts/enums/RoleNames';

let request: SuperTest<Test>;
let application: Application;
let em: EntityManager<IDatabaseDriver<Connection>> | undefined = undefined;
let curatorToken: string;
let createdRequirementId: string;

describe('))))))))))))) Curator Requirement Tests ((((((((((((((', async () => {
  before(async () => {
    application = new Application();
    await application.init();
    await application.start();
    em = application.orm?.em.fork();
    request = supertest(application.app);
    // user <EMAIL>
    // this is the prefix for monumentsMen (see loadFixtures.ts)
    curatorToken = getToken({
      [PayloadKeys.USER_KEY]: Id.simpleId(3, ID_PREFIX_0),
      [PayloadKeys.TENANT_KEY]: Id.simpleId(1),
      [PayloadKeys.ROLES_KEY]: [roleMap[RoleNames.CURATOR]],
    }).token;
    restoreDbFromLocalBackup();
    console.log('🚀 Database cleared, fixtures loaded');
  });

  after(async () => {
    application.server?.close();
  });

  const opportunityId = Id.simpleId(3, ID_PREFIX_0);

  it('should create requirement', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `mutation {
          createRequirement (
            input: {
              source: "https://example.com",
              title: "Test Requirement",
              poc: "John Doe"
            },
            links: {
              opportunityId: "${opportunityId}"
            }
          ){
            id source title poc
            opportunity {
              id
            }
          }
        }
        `,
      })
      .expect(200);

    expect(response.body.data.createRequirement).to.be.a('object');
    expect(response.body.data.createRequirement.id).to.not.be.null;
    expect(response.body.data.createRequirement.source).equal('https://example.com');
    expect(response.body.data.createRequirement.title).equal('Test Requirement');
    expect(response.body.data.createRequirement.poc).equal('John Doe');
    expect(response.body.data.createRequirement.opportunity.id).equal(Id.simpleId(3, ID_PREFIX_0));
    createdRequirementId = response.body.data.createRequirement.id;
  });

  it('should update requirement', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `mutation {
        updateRequirement (input: {
          source: "https://updated-example.com",
          title: "Updated Requirement",
          poc: "Jane Doe"
        }, id: "${createdRequirementId}") {
          id source title poc
          opportunity {
            id
          }
        }
      }
      `,
      })
      .expect(200);
    expect(response.body.data.updateRequirement).to.be.a('object');
    expect(response.body.data.updateRequirement.source).equal('https://updated-example.com');
    expect(response.body.data.updateRequirement.title).equal('Updated Requirement');
    expect(response.body.data.updateRequirement.poc).equal('Jane Doe');
  });

  it('should delete requirement', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `mutation {
          deleteRequirement (id: "${createdRequirementId}")
        }
        `,
      })
      .expect(200);

    expect(response.body.data.deleteRequirement).to.be.true;
  });
});
