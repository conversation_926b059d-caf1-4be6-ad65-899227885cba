import { Connection, EntityManager, IDatabaseDriver } from '@mikro-orm/core';
import Application from 'api/application';
import { getToken } from 'core/auth/authUtils';
import { expect } from 'chai';
import {
  ATTACHMENT_FILE_NAME,
  ID_PREFIX_0,
  ID_PREFIX_1,
  MATCH_SUBSTRING,
  OPS_PER_TENANT,
  restoreDbFromLocalBackup,
} from 'loadFixtures';
import supertest, { SuperTest, Test } from 'supertest';
import { Id } from 'core/utils/Id';
import { DEFAULT_PAGE_SIZE } from 'core/storage/queryUtils';
import { PayloadKeys } from 'core/auth/JwtPayload';
import { RoleNames, roleMap } from 'core/contracts/enums/RoleNames';
import { SearchOperator } from 'core/contracts/enums/SearchOperator';

let request: SuperTest<Test>;
let application: Application;
let em: EntityManager<IDatabaseDriver<Connection>> | undefined = undefined;
let curatorToken: string;
let analystToken: string;

describe(')))))))))))))) API Opportunity Tests ((((((((((((((', async () => {
  before(async () => {
    application = new Application();
    await application.init();
    await application.start();
    em = application.orm?.em.fork();
    request = supertest(application.app);
    // user <EMAIL>
    // this is the prefix for users for monumentsMen (see loadFixtures.ts)
    curatorToken = getToken({
      [PayloadKeys.USER_KEY]: Id.simpleId(3, ID_PREFIX_0),
      [PayloadKeys.TENANT_KEY]: Id.simpleId(1),
      [PayloadKeys.ROLES_KEY]: [roleMap[RoleNames.CURATOR]],
    }).token;
    analystToken = getToken({
      [PayloadKeys.USER_KEY]: Id.simpleId(5, ID_PREFIX_0),
      [PayloadKeys.TENANT_KEY]: Id.simpleId(1),
      [PayloadKeys.ROLES_KEY]: [roleMap[RoleNames.CURATOR], roleMap[RoleNames.ANALYST]],
    }).token;
    restoreDbFromLocalBackup();
    console.log('🚀 Database cleared, fixtures loaded');
  });

  // beforeEach(async () => {
  //   restoreDbFromLocalBackup();
  //   console.log('🚀 Database cleared, fixtures loaded');
  // });

  after(async () => {
    application.server?.close();
  });

  it('should get opportunity by id', async () => {
    const response = await request
      .get(`/opportunities/${Id.simpleId(1, ID_PREFIX_0)}`)
      .set('Authorization', `Bearer ${curatorToken}`)
      .send()
      .expect(200);

    expect(response.body).to.be.a('object');
    expect(response.body.id).equal(Id.simpleId(1, ID_PREFIX_0));
    expect(response.body.user.id).equal(Id.simpleId(1, ID_PREFIX_0));
    expect(response.body.tenant.id).equal(Id.simpleId(1));
    expect(response.body.categories[0].id).equal(Id.simpleId(1, ID_PREFIX_0));
    expect(response.body.stakeholders[0].id).equal(Id.simpleId(1, ID_PREFIX_0));
    expect(response.body.attachments[0].name).equal(ATTACHMENT_FILE_NAME);
    expect(response.body.linkedOpportunityIds.length).equal(3);
    expect(response.body.linkedOpportunityIds).contains(Id.simpleId(5, ID_PREFIX_0));
    expect(response.body.childOpportunityIds.length).equal(3);
    expect(response.body.childOpportunityIds).contains(Id.simpleId(3, ID_PREFIX_0));
    expect(response.body.submissions[0].title).equal(response.body.title);
  });

  it('should get opportunity from other tenant', async () => {
    const response = await request
      .get(`/opportunities/${Id.simpleId(2, ID_PREFIX_1)}`)
      .set('Authorization', `Bearer ${analystToken}`)
      .send()
      .expect(200);

    expect(response.body).to.be.a('object');
    expect(response.body.id).equal(Id.simpleId(2, ID_PREFIX_1));
    expect(response.body.tenant.id).equal(Id.simpleId(2, ID_PREFIX_0));
  });

  it('search opportunities for string OR fieldnames', async () => {
    const response = await request
      .post('/opportunities')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        searchSortInput: {
          searchFields: [
            { fieldNames: ['title', 'statement'], operator: SearchOperator.MATCH, searchValue: MATCH_SUBSTRING },
          ],
        },
      })
      .expect(200);
    expect(response.body.results.length).equal(4);
  });

  // this should match category_1 and category_10 for 2 opps each = 4
  it('search opportunities for category names', async () => {
    const response = await request
      .post('/opportunities')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        searchSortInput: {
          searchFields: [
            { fieldNames: ['categories.name'], operator: SearchOperator.MATCH, searchValue: 'category_1' },
          ],
        },
      })
      .expect(200);
    expect(response.body.results.length).equal(5);
  });

  it('search opportunities for string AND', async () => {
    const response = await request
      .post('/opportunities')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        searchSortInput: {
          searchFields: [
            { fieldNames: ['title'], operator: SearchOperator.MATCH, searchValue: MATCH_SUBSTRING },
            { fieldNames: ['statement'], operator: SearchOperator.MATCH, searchValue: MATCH_SUBSTRING },
          ],
        },
      })
      .expect(200);
    expect(response.body.results.length).equal(2);
  });

  it('filter opportunities by status using OR', async () => {
    const response = await request
      .post('/opportunities')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        searchSortInput: {
          searchFields: [
            { fieldNames: ['status'], operator: SearchOperator.IN, searchValue: ['Approved', 'Archived'] },
          ],
        },
      })
      .expect(200);
    expect(response.body.results).to.be.a('array');
    expect(response.body.results.length).equal(13);
  });

  // Note: enum input values have to be treated as strings when usinjg searchFieldGroups because of JSONType // same as above example but using jsonSearchGroups
  it('filter opportunities using OR', async () => {
    const response = await request
      .post('/opportunities')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        searchSortInput: {
          jsonSearchGroups: [
            {
              operator: 'or',
              operands: [
                { fieldNames: ['status'], operator: '=', searchValue: 'Approved' },
                { fieldNames: ['status'], operator: '=', searchValue: 'Archived' },
              ],
            },
          ],
        },
      })
      .expect(200);
    expect(response.body.results).to.be.a('array');
    expect(response.body.results.length).equal(13);
  });

  it('filter opportunities by deleted status only', async () => {
    const response = await request
      .post('/opportunities')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        searchSortInput: {
          searchFields: [
            { fieldNames: ['status'], operator: SearchOperator.EQ, searchValue: ['Deleted'] },
          ],
        },
      })
      .expect(200);
    expect(response.body.results).to.be.a('array');
    expect(response.body.results.length).equal(6);
    expect(response.body.results.map((r: any) => r.status)).contains('Deleted');
  });

  it('filter opportunities by deleted status using OR', async () => {
    const response = await request
      .post('/opportunities')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        searchSortInput: {
          jsonSearchGroups: [
            {
              operator: 'or',
              operands: [
                { fieldNames: ['status'], operator: '=', searchValue: 'Approved' },
                { fieldNames: ['status'], operator: '=', searchValue: 'Deleted' },
              ],
            },
          ],
        },
      })
      .expect(200);
    expect(response.body.results).to.be.a('array');
    expect(response.body.results.length).equal(13);
    expect(response.body.results.map((r: any) => r.status)).contains('Deleted');
  });

  it('Simulate downoad query with no deleted opportunities', async () => {
    const response = await request
      .post('/opportunities')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        searchSortInput: {
          searchFields: [
            { fieldNames: ['status'], operator: SearchOperator.NE, searchValue: ['Deleted'] },
          ],
        },
      })
      .expect(200);
    expect(response.body.results).to.be.a('array');
    expect(response.body.results.length).equal(19);
    expect(response.body.results.map((r: any) => r.status)).not.contain('Deleted');
  });

  // Note: enum input values have to be treated as strings when usinjg searchFieldGroups because of JSONType
  // category_1* should match 4 opps (category_1 / category_10) and catery_2* should match 2 ops but 1 of those is pending so ultimately only 1
  // this is a total of 5 opps
  it('filter opportunities using OR and AND', async () => {
    const response = await request
      .post('/opportunities')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        searchSortInput: {
          jsonSearchGroups: [
            {
              operator: 'and',
              operands: [
                {
                  operator: 'or',
                  operands: [
                    { fieldNames: ['status'], operator: '=', searchValue: 'Approved' },
                    { fieldNames: ['status'], operator: '=', searchValue: 'Archived' },
                  ],
                },
                {
                  operator: 'or',
                  operands: [
                    { fieldNames: ['categories.name'], operator: '~', searchValue: 'category_1' },
                    { fieldNames: ['categories.name'], operator: '~', searchValue: 'category_2' },
                  ],
                },
              ],
            },
          ],
        },
      })
      .expect(200);
    expect(response.body.results).to.be.a('array');
    expect(response.body.results.length).equal(5);
  });

  // Note: enum input values have to be treated as strings when usinjg searchFieldGroups because of JSONType
  // category_1* should match 4 opps (category_1 / category_10) and catery_2* should match 2 ops but 1 of those is pending so ultimately only 1
  // this is a total of 5 opps
  it('filter opportunities using OR and AND by using both searchFields and jsonSearchGroups', async () => {
    const response = await request
      .post('/opportunities')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        searchSortInput: {
          searchFields: [
            { fieldNames: ['status'], operator: SearchOperator.IN, searchValue: ['Approved', 'Archived'] },
          ],
          jsonSearchGroups: [
            {
              operator: 'or',
              operands: [
                { fieldNames: ['categories.name'], operator: '~', searchValue: 'category_1' },
                { fieldNames: ['categories.name'], operator: '~', searchValue: 'category_2' },
              ],
            },
          ],
        },
      })
      .expect(200);
    expect(response.body.results).to.be.a('array');
    expect(response.body.results.length).equal(5);
  });

  it('filter opportunities by date (since <some date>)', async () => {
    const response = await request
      .post('/opportunities')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        searchSortInput: {
          searchFields: [
            { fieldNames: ['createdAt'], operator: SearchOperator.GTE, searchValue: '2021-10-10T00:00:00.000Z' },
          ],
        },
      })
      .expect(200);
    expect(response.body.results).to.be.a('array');
    expect(response.body.pageInfo.hasNext).equal(false);
    expect(response.body.pageInfo.hasPrevious).equal(false);
    expect(response.body.pageInfo.lastCursor).equal('0');
    expect(response.body.pageInfo.lastPageSize).equal(DEFAULT_PAGE_SIZE);
    expect(response.body.pageInfo.retrievedCount).equal(OPS_PER_TENANT);
    // should be all bootstraped opportunities
    expect(response.body.results.length).equal(OPS_PER_TENANT);
  });

  it('page opportunities and sort', async () => {
    const response = await request
      .post('/opportunities')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        pagingInput: { pageSize: 20, cursor: '0' },
        searchSortInput: {
          sortFields: [
            { fieldName: 'id', ascending: false },
            { fieldName: 'title', ascending: true },
          ],
        },
      })
      .expect(200);
    expect(response.body.results).to.be.a('array');
    expect(response.body.results.length).equal(20);
    expect(response.body.pageInfo.hasNext).equal(true);
    expect(response.body.pageInfo.hasPrevious).equal(false);
    expect(response.body.pageInfo.lastCursor).equal('0');
    expect(response.body.pageInfo.lastPageSize).equal(20);
    expect(response.body.pageInfo.retrievedCount).equal(20);
    expect(response.body.pageInfo.totalCount).equal(OPS_PER_TENANT);
    expect(response.body.results[0].id).equal(Id.simpleId(OPS_PER_TENANT, ID_PREFIX_0));
    expect(response.body.results[19].id).equal(Id.simpleId(6, ID_PREFIX_0));
  });
});
