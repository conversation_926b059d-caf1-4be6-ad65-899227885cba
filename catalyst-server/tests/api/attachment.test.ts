import { Connection, EntityManager, IDatabaseDriver } from '@mikro-orm/core';
import Application from 'api/application';
import { expect } from 'chai';
import { PayloadKeys } from 'core/auth/JwtPayload';
import { getToken } from 'core/auth/authUtils';
import { RoleNames, roleMap } from 'core/contracts/enums/RoleNames';
import { Id } from 'core/utils/Id';
import { ID_PREFIX_0, restoreDbFromLocalBackup } from 'loadFixtures';
import supertest, { SuperTest, Test } from 'supertest';

let request: SuperTest<Test>;
let application: Application;
let em: EntityManager<IDatabaseDriver<Connection>> | undefined = undefined;
let curatorToken: string;

describe(')))))))))))))) API Opportunity Tests ((((((((((((((', async () => {
  before(async () => {
    application = new Application();
    await application.init();
    await application.start();
    em = application.orm?.em.fork();
    request = supertest(application.app);
    // user <EMAIL>
    // this is the prefix for users for monumentsMen (see loadFixtures.ts)
    curatorToken = getToken({
      [PayloadKeys.USER_KEY]: Id.simpleId(3, ID_PREFIX_0),
      [PayloadKeys.TENANT_KEY]: Id.simpleId(1),
      [PayloadKeys.ROLES_KEY]: [roleMap[RoleNames.CURATOR]],
    }).token;
    restoreDbFromLocalBackup();
    console.log('🚀 Database cleared, fixtures loaded');
  });

  // beforeEach(async () => {
  //   restoreDbFromLocalBackup();
  //   console.log('🚀 Database cleared, fixtures loaded');
  // });

  after(async () => {
    application.server?.close();
  });

  it('should retrieve the attachment location', async () => {
    const response = await request
      .get(`/attachments/${Id.simpleId(1, ID_PREFIX_0)}`)
      .set('Authorization', `Bearer ${curatorToken}`)
      .send()
      .expect(200);
    expect(response.body).to.be.a('object');
    expect(response.body.location).to.contain('https://');
    expect(response.body.location).to.contain(Id.simpleId(1, ID_PREFIX_0));
  });
});
