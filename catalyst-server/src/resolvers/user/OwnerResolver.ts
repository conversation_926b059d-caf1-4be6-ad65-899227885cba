import { AppContext } from 'core/core';
import { GraphQLResolveInfo } from 'graphql';
import { Arg, Ctx, Info, Mutation, Query, Resolver } from 'type-graphql';
import { fieldsToRelations } from 'core/utils/graphqlUtils';
import { PagingInput, Scope, SearchSortInput } from 'core/contracts/input/base/CommonInput';
import { errorKeys } from 'core/contracts/errors/ErrorCodes';
import { PayloadKeys } from 'core/auth/JwtPayload';
import { Owner } from 'core/entities/Owner';
import { CreateOwnerInput, OwnerLinks, UpdateOwnerInput } from 'core/contracts/input/user/OwnerInput';
import { OwnerController } from 'core/controllers/user/OwnerController';
import { verifyScope } from 'core/auth/authUtils';
import { isEmptyScope } from 'core/controllers/controllers';
import { getEmptyPageInfo } from 'core/storage/queryUtils';
import { OwnerPage } from 'core/contracts/output/Page';

type VerifyEntry = {
  tenantId: string;
  creatorId: string;
};

@Resolver(() => Owner)
export class OwnerResolver {
  @Query(() => OwnerPage, { description: 'Get a page of matching Owners' })
  public async queryOwners(
    @Ctx() ctx: AppContext,
    @Info() info: GraphQLResolveInfo,
    @Arg('pagingInput', { nullable: true }) pagingInput?: PagingInput,
    @Arg('searchSortInput', { nullable: true }) searchSortInput?: SearchSortInput,
    @Arg('scope', { nullable: true }) scope?: Scope,
  ): Promise<OwnerPage> {
    if (isEmptyScope(scope)) return { results: [], pageInfo: getEmptyPageInfo() }; // empty scope returns empty page
    if (scope && !(await verifyScope({ scope, context: ctx }))) throw new Error(errorKeys.UNAUTHORIZED);
    const { tenantId } = OwnerResolver.verifyTenant(ctx);
    const relationPaths = fieldsToRelations(info, { root: 'results' });
    return OwnerController.queryOwners({
      em: ctx.em,
      tenantId,
      pagingInput,
      searchSortInput,
      relationPaths,
    });
  }

  @Query(() => Owner, { nullable: true, description: 'Retrieve an Owner' })
  public async getOwner(
    @Arg('id') id: string,
    @Ctx() ctx: AppContext,
    @Info() info: GraphQLResolveInfo,
    @Arg('scope', { nullable: true }) scope?: Scope,
  ): Promise<Owner | null> {
    const { tenantId } = OwnerResolver.verifyTenant(ctx);
    const relationPaths = fieldsToRelations(info) as never;
    return OwnerController.getOwner({ em: ctx.em, id, tenantId, relationPaths });
  }

  @Mutation(() => Owner, { description: 'Create a new Owner for opportunity' })
  public async createOwner(
    @Arg('input') input: CreateOwnerInput,
    @Ctx() ctx: AppContext,
    @Info() info: GraphQLResolveInfo,
    @Arg('links', { nullable: true }) links?: OwnerLinks,
  ): Promise<Owner> {
    const { tenantId } = OwnerResolver.verifyTenant(ctx);
    const relationPaths = fieldsToRelations(info);
    const userPath = fieldsToRelations(info, { root: 'user' });
    const opportunityPaths = fieldsToRelations(info, { root: 'opportunities' });
    return OwnerController.createOwner(ctx.em, tenantId, input, relationPaths, userPath, opportunityPaths, links);
  }

  @Mutation(() => Owner, { description: 'Update an existing Owner' })
  public async updateOwner(
    @Arg('input') input: UpdateOwnerInput,
    @Arg('id') id: string,
    @Ctx() ctx: AppContext,
    @Info() info: GraphQLResolveInfo,
    @Arg('links', { nullable: true }) links?: OwnerLinks,
  ): Promise<Owner> {
    const { tenantId } = OwnerResolver.verifyTenant(ctx);
    const relationPaths = fieldsToRelations(info);
    const userPath = fieldsToRelations(info, { root: 'user' });
    const opportunityPaths = fieldsToRelations(info, { root: 'opportunities' });

    return OwnerController.updateOwner(ctx.em, tenantId, id, input, relationPaths, userPath, opportunityPaths, links);
  }

  private static verifyTenant(ctx: AppContext): VerifyEntry {
    const tenantId = ctx.token?.[PayloadKeys.TENANT_KEY];
    if (!tenantId) throw Error(errorKeys.TENANT_NOT_FOUND);
    const creatorId = ctx.token?.[PayloadKeys.USER_KEY];
    if (!creatorId) throw new Error(errorKeys.UNVERIFIED_USER_NOT_FOUND);
    return { tenantId, creatorId };
  }
}
