import { QueryOrder } from '@mikro-orm/core';
import { AppContext } from 'core/core';
import { RoleNames } from 'core/contracts/enums/RoleNames';
import { errorKeys } from 'core/contracts/errors/ErrorCodes';
import { PagingInput, SearchSortInput } from 'core/contracts/input/base/CommonInput';
import { CreateStakeholderInput, UpdateStakeholderInput } from 'core/contracts/input/curator/StakeholderInput';
import { StakeholderPage } from 'core/contracts/output/Page';
import { Stakeholder } from 'core/entities/Stakeholder';
import { Tenant } from 'core/entities/Tenant';
import { GraphQLResolveInfo } from 'graphql';
import { Arg, Authorized, Ctx, Info, Int, Mutation, Query, Resolver } from 'type-graphql';
import { fieldsToRelations } from 'core/utils/graphqlUtils';
import {
  caseInsensitiveMatchValue,
  combineSortFields,
  defaultSecondarySortFields,
  getPageInfo,
  getQueryBounds,
  getSearchFilter,
  getSortFilter,
} from 'core/storage/queryUtils';
import { ProjectStakeholder } from 'core/entities/ProjectStakeholder';
import { PayloadKeys } from 'core/auth/JwtPayload';

@Resolver(() => Stakeholder)
export class StakeholderResolver {
  /***
   *       ___       __  __            __  _          __         __  ____
   *      / _ |__ __/ /_/ /  ___ ___  / /_(_)______ _/ /____ ___/ / / __ \___  ___
   *     / __ / // / __/ _ \/ -_) _ \/ __/ / __/ _ `/ __/ -_) _  / / /_/ / _ \(_-<
   *    /_/ |_\_,_/\__/_//_/\__/_//_/\__/_/\__/\_,_/\__/\__/\_,_/  \____/ .__/___/
   *                                                                   /_/
   */

  @Authorized(RoleNames.CURATOR)
  @Query(() => StakeholderPage, {
    description: 'Get a page of matching Stakeholders',
  })
  public async queryStakeholders(
    @Ctx() ctx: AppContext,
    @Info() info: GraphQLResolveInfo,
    @Arg('pagingInput', { nullable: true }) pagingInput?: PagingInput,
    @Arg('searchSortInput', { nullable: true })
    searchSortInput?: SearchSortInput,
  ): Promise<StakeholderPage> {
    const relationPaths = fieldsToRelations(info, { root: 'results' }) as never;
    const { limit, offset } = getQueryBounds(pagingInput);
    const filter = {
      $and: [
        { tenant: { id: ctx.token?.[PayloadKeys.TENANT_KEY] } },
        ...getSearchFilter<Stakeholder>(searchSortInput?.searchFields),
      ],
    };
    const orderBy = getSortFilter(
      combineSortFields(searchSortInput?.sortFields, defaultSecondarySortFields(), [
        { fieldName: 'name', ascending: true },
      ]),
    );
    const [results, totalCount] = await ctx.em
      .getRepository(Stakeholder)
      .findAndCount(filter, { populate: relationPaths, orderBy, limit, offset });
    return {
      results,
      pageInfo: getPageInfo(totalCount, results.length, limit, offset),
    };
  }

  @Authorized(RoleNames.CURATOR)
  @Query(() => Stakeholder, {
    nullable: true,
    description: 'Get the Stakeholder',
  })
  public async getStakeholder(
    @Ctx() ctx: AppContext,
    @Info() info: GraphQLResolveInfo,
    @Arg('id') id: string,
  ): Promise<Stakeholder | null> {
    const relationPaths = fieldsToRelations(info) as never;
    return ctx.em
      .getRepository(Stakeholder)
      .findOne({ id, tenant: { id: ctx.token?.[PayloadKeys.TENANT_KEY] } }, { populate: relationPaths });
  }

  @Authorized(RoleNames.CURATOR)
  @Mutation(() => Stakeholder, { description: 'Create a new Stakeholder' })
  public async createStakeholder(
    @Arg('input') input: CreateStakeholderInput,
    @Ctx() ctx: AppContext,
    @Info() info: GraphQLResolveInfo,
  ): Promise<Stakeholder> {
    const tenant = await ctx.em.getRepository(Tenant).findOne({ id: ctx.token?.[PayloadKeys.TENANT_KEY] });
    if (!tenant) throw Error(errorKeys.TENANT_NOT_FOUND);

    // If we have no valid input values, throw an error. We do this check because an empty string could be passed in.
    const lowerCaseFilterInput = Object.keys(input).reduce((acc, key) => {
      const value = input[key as keyof CreateStakeholderInput]; // Type assertion for key
      if (value) {
        acc[key as keyof CreateStakeholderInput] = caseInsensitiveMatchValue(value) as string;
      }
      return acc;
    }, {} as Partial<CreateStakeholderInput>);

    if (Object.keys(lowerCaseFilterInput).length === 0) throw Error(errorKeys.ARGUMENT_VALIDATION_ERROR);

    const stakeholder = Stakeholder.newStakeholder(input);
    stakeholder.tenant = tenant;
    await ctx.em.persist(stakeholder).flush();
    return stakeholder;
  }

  @Authorized(RoleNames.CURATOR)
  @Mutation(() => Stakeholder, {
    description: 'Update an existing Stakeholder',
  })
  public async updateStakeholder(
    @Arg('input') input: UpdateStakeholderInput,
    @Arg('id') id: string,
    @Ctx() ctx: AppContext,
    @Info() info: GraphQLResolveInfo,
  ): Promise<Stakeholder> {
    const relationPaths = fieldsToRelations(info) as never;
    const stakeholder = await ctx.em
      .getRepository(Stakeholder)
      .findOne({ id, tenant: { id: ctx.token?.[PayloadKeys.TENANT_KEY] } }, { populate: relationPaths });
    if (!stakeholder) throw Error(errorKeys.OBJECT_NOT_FOUND);
    stakeholder.modify(input);
    await ctx.em.persist(stakeholder).flush();
    await ctx.em.populate(stakeholder, relationPaths);
    return stakeholder;
  }

  @Authorized(RoleNames.CURATOR)
  @Mutation(() => Boolean, { description: 'Delete an existing Stakeholder' })
  public async deleteStakeholder(@Arg('id') id: string, @Ctx() ctx: AppContext): Promise<boolean> {
    const stakeholder = await ctx.em
      .getRepository(Stakeholder)
      .findOne({ id, tenant: { id: ctx.token?.[PayloadKeys.TENANT_KEY] } });
    if (!stakeholder) throw Error(errorKeys.OBJECT_NOT_FOUND);
    const projectStakeholders = await ctx.em
      .getRepository(ProjectStakeholder)
      .find({ stakeholder: { id, tenant: { id: ctx.token?.[PayloadKeys.TENANT_KEY] } } });
    !projectStakeholders.length && (await ctx.em.remove(stakeholder).flush());
    return !projectStakeholders.length;
  }
}
