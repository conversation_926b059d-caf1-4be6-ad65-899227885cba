import { PayloadKeys } from 'core/auth/JwtPayload';
import { RoleNames } from 'core/contracts/enums/RoleNames';
import { errorKeys } from 'core/contracts/errors/ErrorCodes';
import { 
  CreateRequirementInput, 
  UpdateRequirementInput, 
  RequirementLinks 
} from 'core/contracts/input/curator/RequirementInput';
import { RequirementController } from 'core/controllers/curator/RequirementController';
import { AppContext } from 'core/core';
import { Requirement } from 'core/entities/Requirement';
import { fieldsToRelations } from 'core/utils/graphqlUtils';
import { GraphQLResolveInfo } from 'graphql';
import { Arg, Authorized, Ctx, Info, Mutation, Query, Resolver } from 'type-graphql';

@Resolver(() => Requirement)
export class RequirementResolver {
  @Authorized(RoleNames.CURATOR)
  @Query(() => Requirement, { nullable: true, description: 'Get a Requirement by ID' })
  public async getRequirement(
    @Arg('id') id: string,
    @Ctx() ctx: AppContext,
    @Info() info: GraphQLResolveInfo,
  ): Promise<Requirement | null> {
    const tenantId = ctx.token?.[PayloadKeys.TENANT_KEY];
    if (!tenantId) throw new Error(errorKeys.TENANT_NOT_FOUND);

    const relationPaths = fieldsToRelations(info) as string[];
    return RequirementController.getRequirement({
      em: ctx.em,
      id,
      tenantId,
      relationPaths,
    });
  }

  @Authorized(RoleNames.CURATOR)
  @Query(() => [Requirement], { description: 'Get all Requirements for an Opportunity' })
  public async getRequirementsByOpportunity(
    @Arg('opportunityId') opportunityId: string,
    @Ctx() ctx: AppContext,
    @Info() info: GraphQLResolveInfo,
  ): Promise<Requirement[]> {
    const tenantId = ctx.token?.[PayloadKeys.TENANT_KEY];
    if (!tenantId) throw new Error(errorKeys.TENANT_NOT_FOUND);

    const relationPaths = fieldsToRelations(info) as string[];
    return RequirementController.getRequirementsByOpportunity({
      em: ctx.em,
      opportunityId,
      tenantId,
      relationPaths,
    });
  }

  @Authorized(RoleNames.CURATOR)
  @Mutation(() => Requirement, { description: 'Create a new Requirement' })
  public async createRequirement(
    @Arg('input') input: CreateRequirementInput,
    @Arg('links') links: RequirementLinks,
    @Ctx() ctx: AppContext,
    @Info() info: GraphQLResolveInfo,
  ): Promise<Requirement> {
    const tenantId = ctx.token?.[PayloadKeys.TENANT_KEY];
    if (!tenantId) throw new Error(errorKeys.TENANT_NOT_FOUND);

    const relationPaths = fieldsToRelations(info) as string[];
    return RequirementController.createRequirement({
      em: ctx.em,
      input,
      opportunityId: links.opportunityId,
      tenantId,
      relationPaths,
    });
  }

  @Authorized(RoleNames.CURATOR)
  @Mutation(() => Requirement, { description: 'Update an existing Requirement' })
  public async updateRequirement(
    @Arg('id') id: string,
    @Arg('input') input: UpdateRequirementInput,
    @Ctx() ctx: AppContext,
    @Info() info: GraphQLResolveInfo,
  ): Promise<Requirement> {
    const tenantId = ctx.token?.[PayloadKeys.TENANT_KEY];
    if (!tenantId) throw new Error(errorKeys.TENANT_NOT_FOUND);

    const relationPaths = fieldsToRelations(info) as string[];
    return RequirementController.updateRequirement({
      em: ctx.em,
      id,
      input,
      tenantId,
      relationPaths,
    });
  }

  @Authorized(RoleNames.CURATOR)
  @Mutation(() => Boolean, { description: 'Delete an existing Requirement' })
  public async deleteRequirement(@Arg('id') id: string, @Ctx() ctx: AppContext): Promise<boolean> {
    const tenantId = ctx.token?.[PayloadKeys.TENANT_KEY];
    if (!tenantId) throw new Error(errorKeys.TENANT_NOT_FOUND);

    return RequirementController.deleteRequirement({
      em: ctx.em,
      id,
      tenantId,
    });
  }
}
