{"openapi": "3.0.0", "components": {"examples": {}, "headers": {}, "parameters": {}, "requestBodies": {}, "responses": {}, "schemas": {"Privilege": {"properties": {"id": {}, "name": {}, "resourceId": {}, "resourceType": {}}, "required": ["id", "name", "resourceId", "resourceType"], "type": "object", "additionalProperties": false}, "PrivilegeGroup": {"properties": {"id": {"type": "string"}, "name": {"type": "string"}, "privileges": {"items": {"$ref": "#/components/schemas/Privilege"}, "type": "array"}}, "required": ["id", "name", "privileges"], "type": "object", "additionalProperties": false}, "User": {"description": "A user of the system", "properties": {"id": {"type": "string"}, "emailAddress": {"type": "string"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "org1": {"type": "string"}, "org2": {"type": "string"}, "org3": {"type": "string"}, "org4": {"type": "string"}, "phone": {"type": "string"}, "altContact": {"type": "string"}, "privilegeGroups": {"items": {"$ref": "#/components/schemas/PrivilegeGroup"}, "type": "array"}}, "required": ["id", "emailAddress", "firstName", "lastName", "privilegeGroups"], "type": "object", "additionalProperties": false}, "AuthResponse": {"description": "The response object for a successful login request", "properties": {"user": {"$ref": "#/components/schemas/User", "description": "The currently logged in user"}, "token": {"type": "string", "description": "The token to use for subsequent requests"}, "expiresAt": {"type": "string", "format": "date-time", "description": "* The time at which the token expires"}}, "required": ["token", "expiresAt"], "type": "object", "additionalProperties": false}, "AuthInput": {"description": "Defines required input for the authentication operation.", "properties": {"userName": {"type": "string"}, "password": {"type": "string"}, "tenantHandle": {"type": "string"}}, "required": ["userName", "password", "tenantHandle"], "type": "object", "additionalProperties": false}, "Tenant": {"description": "The associated tenant information", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "handle": {"type": "string"}, "label": {"type": "string"}}, "required": ["id", "name", "handle"], "type": "object", "additionalProperties": false}, "Category": {"description": "Category or 'tag'", "properties": {"id": {"type": "string"}, "name": {"type": "string"}}, "required": ["id", "name"], "type": "object", "additionalProperties": false}, "Stakeholder": {"description": "Representation of a stakeholder associtated with an opportunity", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "org": {"type": "string"}}, "required": ["id"], "type": "object", "additionalProperties": false}, "OpportunityOwnerStatus": {"description": "A user of the system", "properties": {"id": {"type": "string"}, "status": {"$ref": "#/components/schemas/OwnershipStatus"}, "statusSetPreviousAt": {"type": "string", "format": "date-time"}, "statusSetRemovedAt": {"type": "string", "format": "date-time"}, "owner": {"$ref": "#/components/schemas/Owner"}, "opportunity": {"$ref": "#/components/schemas/Opportunity"}}, "required": ["id", "owner", "opportunity"], "type": "object", "additionalProperties": false}, "Owner": {"description": "A user of the system", "properties": {"id": {"type": "string"}, "organizationRole": {"type": "string"}, "opportunityOwnerStatuses": {"items": {"$ref": "#/components/schemas/OpportunityOwnerStatus"}, "type": "array"}, "user": {"$ref": "#/components/schemas/User"}}, "required": ["id", "opportunityOwnerStatuses", "user"], "type": "object", "additionalProperties": false}, "Opportunity": {"description": "An opportunity for improvement or innovation", "properties": {"function": {"type": "string"}, "id": {"type": "string"}, "org1": {"type": "string"}, "org2": {"type": "string"}, "org3": {"type": "string"}, "org4": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time", "default": "2025-07-29T20:46:50.347Z"}, "updatedAt": {"type": "string", "format": "date-time"}, "lastCurated": {"type": "string", "format": "date-time"}, "title": {"type": "string"}, "statement": {"type": "string"}, "status": {"$ref": "#/components/schemas/OpportunityStatus"}, "context": {"type": "string"}, "benefits": {"type": "string"}, "solutionConcepts": {"type": "string"}, "additionalNotes": {"type": "string"}, "campaign": {"type": "string"}, "campaignNotes": {"type": "string"}, "statusNotes": {"type": "string"}, "priority": {"type": "number", "format": "double", "default": 0}, "priorityNotes": {"type": "string"}, "solutionPathway": {"type": "string"}, "armyModernizationPriority": {"type": "string"}, "solutionPathwayDetails": {"type": "string"}, "attachmentNotes": {"type": "string"}, "initiatives": {"type": "string"}, "endorsements": {"type": "string"}, "echelonApplicability": {"type": "string"}, "isTiCLOE": {"$ref": "#/components/schemas/IsTiCLOEType"}, "transitionInContactLineOfEffort": {"type": "string"}, "operationalRoles": {"items": {"type": "string"}, "type": "array"}, "capabilityArea": {"items": {"type": "string"}, "type": "array"}, "tenant": {"$ref": "#/components/schemas/Tenant"}, "user": {"$ref": "#/components/schemas/User"}, "categories": {"items": {"$ref": "#/components/schemas/Category"}, "type": "array"}, "stakeholders": {"items": {"$ref": "#/components/schemas/Stakeholder"}, "type": "array"}, "opportunityOwnerStatuses": {"items": {"$ref": "#/components/schemas/OpportunityOwnerStatus"}, "type": "array"}, "linkedOpportunityIds": {"items": {"type": "string"}, "type": "array"}, "parentOpportunitityIds": {"items": {"type": "string"}, "type": "array"}, "childOpportunityIds": {"items": {"type": "string"}, "type": "array"}, "attachments": {"items": {"$ref": "#/components/schemas/Attachment"}, "type": "array"}, "submissions": {"items": {"$ref": "#/components/schemas/Submission"}, "type": "array"}}, "required": ["id", "title", "statement", "context", "tenant", "user", "categories", "stakeholders", "opportunityOwnerStatuses", "linkedOpportunityIds", "parentOpportunitityIds", "childOpportunityIds", "attachments", "submissions"], "type": "object", "additionalProperties": false}, "OwnershipStatus": {"enum": ["Current", "Previous", "Initial", "Removed"], "type": "string"}, "Attachment": {"description": "Reprsentation of a file attachment", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "encoding": {"type": "string"}, "mimetype": {"type": "string"}}, "required": ["id", "name"], "type": "object", "additionalProperties": false}, "Submission": {"description": "The orginal submission source of the opportunity", "properties": {"function": {"type": "string"}, "id": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time", "default": "2025-07-29T20:46:50.346Z"}, "updatedAt": {"type": "string", "format": "date-time"}, "title": {"type": "string"}, "statement": {"type": "string"}, "context": {"type": "string"}, "benefits": {"type": "string"}, "solutionConcepts": {"type": "string"}, "campaign": {"type": "string"}}, "required": ["id", "title", "statement", "context"], "type": "object", "additionalProperties": false}, "OpportunityStatus": {"enum": ["Pending", "Approved", "Archived", "Deleted"], "type": "string"}, "IsTiCLOEType": {"enum": ["Yes", "No", "Undefined"], "type": "string"}, "PageInfo": {"properties": {"hasNext": {"type": "boolean", "default": false}, "hasPrevious": {"type": "boolean", "default": false}, "lastCursor": {"type": "string"}, "lastPageSize": {"type": "number", "format": "double", "default": 0}, "retrievedCount": {"type": "number", "format": "double", "default": 0}, "totalCount": {"type": "number", "format": "double", "default": 0}}, "type": "object", "additionalProperties": false}, "Page_Opportunity_": {"description": "A page (set) of results", "properties": {"results": {"items": {"$ref": "#/components/schemas/Opportunity"}, "type": "array", "description": "The results of the query"}, "pageInfo": {"$ref": "#/components/schemas/PageInfo", "description": "Meta information about the query results"}}, "required": ["results", "pageInfo"], "type": "object", "additionalProperties": false}, "PagingInput": {"description": "Specifies the paging input for the query operation.", "properties": {"pageSize": {"type": "number", "format": "double", "description": "The number of rows to request, per page."}, "cursor": {"type": "string", "description": "How many results to skip or the 'zero-based' starting index. Note: this is a 'string' type, but the value should be an integer"}}, "type": "object", "additionalProperties": false}, "SearchOperator": {"description": "Specifies the type of search operation to perform with the given value.\nOperators are 'equal', 'not equal', 'substring match', 'greater than',\n'less than', 'greater than or equal to', 'less than or equal to', 'in given array', 'not in given array'", "enum": ["=", "!=", "~", ">", "<", ">=", "<=", "in", "nin"], "type": "string"}, "SearchField": {"properties": {"fieldNames": {"items": {"type": "string"}, "type": "array"}, "operator": {"$ref": "#/components/schemas/SearchOperator"}, "searchValue": {"anyOf": [{"type": "string"}, {"type": "number", "format": "double"}, {"type": "boolean"}, {"type": "string", "format": "date-time"}, {"items": {}, "type": "array"}], "nullable": true}}, "required": ["fieldNames"], "type": "object", "additionalProperties": false}, "JsonSearchGroup": {"properties": {"operands": {"items": {"anyOf": [{"$ref": "#/components/schemas/JsonSearchGroup"}, {"$ref": "#/components/schemas/SearchField"}]}, "type": "array"}, "operator": {"$ref": "#/components/schemas/LogicalOperator"}}, "type": "object", "additionalProperties": false}, "LogicalOperator": {"enum": ["or", "and"], "type": "string"}, "SortField": {"properties": {"fieldName": {"type": "string"}, "ascending": {"type": "boolean"}}, "required": ["fieldName"], "type": "object", "additionalProperties": false}, "SearchSortInput": {"properties": {"searchFields": {"items": {"$ref": "#/components/schemas/SearchField"}, "type": "array", "description": "Simple AND query for a set of fieldNames and operators.\nUse JsonSearchGroup to create OR values and more complex queries"}, "jsonSearchGroups": {"items": {"$ref": "#/components/schemas/JsonSearchGroup"}, "type": "array", "description": "Complex query, allowing for nested AND/OR values"}, "sortFields": {"items": {"$ref": "#/components/schemas/SortField"}, "type": "array", "description": "Specifies the fields used for sorting the results"}}, "type": "object", "additionalProperties": false}, "ResourceType": {"enum": ["TENANT"], "type": "string"}, "Resource": {"description": "Specifies the target resource and type", "properties": {"resourceId": {"type": "string"}, "resourceType": {"$ref": "#/components/schemas/ResourceType"}}, "required": ["resourceId", "resourceType"], "type": "object", "additionalProperties": false}, "Scope": {"description": "Specifies the scope for the query operation", "properties": {"resources": {"items": {"$ref": "#/components/schemas/Resource"}, "type": "array"}}, "required": ["resources"], "type": "object", "additionalProperties": false}, "QueryInput": {"description": "Defines the query input for the search operation.  \n\nExample: \n\nFind by substring matching \"test\" in 'title' or 'statement' and created since a specific date\n\n```\n{\n  \"pagingInput\": {\n      \"pagesize\": 20,\n      \"cursor\": \"0\"\n  },\n  \"searchSortInput\": {\n      \"searchFields\": [\n          {\n              \"fieldNames\": [ \"title\", \"statement\" ],\n              \"operator\": \"~\",\n              \"searchvalue\": \"test\"\n          },\n          {\n              \"fieldNames\": [ \"createdAt\" ],\n              \"operator\": \">=\",\n              \"searchValue\": \"2021-10-10T00:00:00.000Z\"\n          }\n      ]\n   }\n}\n```  \n\nExample: \n\nFilter by Approved and Archived statuses - rows 100-199\n\n```\n{\n    \"pagingInput\": {\n        \"pageSize\": 100,\n        \"cursor\": \"100\"\n    },\n    \"searchSortInput\": {\n        \"searchSortInput\": {\n            \"searchFields\": [\n                {\n                    \"fieldNames\": [ \"status\" ],\n                    \"operator\": \"in\",\n                    \"searchValue\": [ \"Approved\", \"Archived\" ]\n                }\n            ]\n        }\n    }\n}\n```\n\nExample:\n\nAdvanced AND/OR search using 'JsonSearchGroups'  \nFilter by \"Approved\" or \"Archived\" statuses and category name substring matching \"category_1\" or \"category_2\"\n\n```\n{\n    \"pagingInput\": {\n        \"pageSize\": 100,\n        \"cursor\": \"100\"\n    },\n    \"searchSortInput\": {\n        \"jsonSearchGroups\": [\n            { \"operator\": \"and\",\n                \"operands\": [\n                    {\n                        \"operator\": \"or\",\n                        \"operands\": [\n                            {\n                                \"fieldNames\": [ \"status\" ],\n                                \"operator\": \"=\",\n                                \"searchValue\": \"Approved\"\n                            },\n                            {\n                                \"fieldNames\": [ \"status\" ],\n                                \"operator\": \"=\",\n                                \"searchValue\": \"Archived\"\n                            }\n                        ]\n                    },\n                    {\n                        \"operator\": \"or\",\n                        \"operands\": [\n                            {\n                                \"fieldNames\": [ \"categories.name\" ],\n                                \"operator\": \"~\",\n                                \"searchValue\": \"category_1\"\n                            },\n                            {\n                                \"fieldNames\": [ \"categories.name\" ],\n                                \"operator\": \"~\",\n                                \"searchValue\": \"category_2\"\n                            }\n                        ]\n                    }\n                ]\n            }\n        ]\n    }\n}\n```", "properties": {"pagingInput": {"$ref": "#/components/schemas/PagingInput", "description": "Specifies the paging params for the query operation"}, "searchSortInput": {"$ref": "#/components/schemas/SearchSortInput", "description": "# Specifies the search and sort input for the query operation"}, "scope": {"$ref": "#/components/schemas/Scope", "description": "Allows for requesting access to additional resources (i.e. other Tenants' data)"}}, "type": "object", "additionalProperties": false}, "Location": {"properties": {"location": {"type": "string", "description": "The location (uri) of the resource"}}, "required": ["location"], "type": "object", "additionalProperties": false}}, "securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}}, "info": {"title": "catalyst-server", "version": "1.29.0", "description": "The Catalyst Server", "license": {"name": "MIT"}, "contact": {"name": "<PERSON>"}}, "paths": {"/login": {"post": {"operationId": "<PERSON><PERSON>", "responses": {"200": {"description": "a response object containing credentials for subsequent requests", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthResponse"}}}}}, "description": "Authenticate a user and return a token for subsequent requests and list of allowed tenants (as privileges).\nThese privileges may be returned in the 'scope' parameter of [QueryInput](#/Opportunities/GetOpportunities) to filter by allowed Tenants.   \n   \nExample:  \n\n\n  ```\n  curl -X POST https://api.soldierinnovation.com/login -H 'Content-Type: application/json' -d '\n  { \"userName\": \"<EMAIL>\", \"password\": \"a.password\", \"tenantHandle\": \"monumentsMen\" }\n  '\n  ```\n Response:   \n  ```\n  {\n    \"user\": {\n        \"id\": \"00000000-0000-0000-0000-000000000005\",\n        \"emailAddress\": \"<EMAIL>\",\n        \"firstName\": \"Abigale\",\n        \"lastName\": \"Deckow\",\n        \"org1\": \"Technical Operations\",\n        <additional fields ...>\n        \"privilegeGroups\": [\n            {\n                \"id\": \"00000000-0000-0000-0000-000000000001\",\n                \"name\": \"All Tenants Group\",\n                \"privileges\": [\n                    {\n                        \"id\": \"f65621c3-038b-4d91-8669-380d86a052b5\",\n                        \"name\": \"Starship Troopers\",\n                        \"resourceId\": \"00000000-0000-0000-0000-000000000002\",\n                        \"resourceType\": \"TENANT\"\n                    },\n                    {\n                        \"id\": \"bf689b68-a594-4006-b779-e16dc6da6fe5\",\n                        \"name\": \"The Monuments Men\",\n                        \"resourceId\": \"00000000-0000-0000-0000-000000000001\",\n                        \"resourceType\": \"TENANT\"\n                    }\n                ]\n            }\n        ]\n    },\n    \"token\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJfMSI6IjAwMDAwMDAwLTAwMDAtMDAwMC0wMDAwLTAwMDAwMDAwMDAwNSIsIl8yIjoiMDAwMDAwMDAtMDAwMC0wMDAwLTAwMDAtMDAwMDAwMDAwMDAxIiwiXzMiOlsiMSIsIjMiXSwiaWF0IjoxNzEyODYyNjQ4LCJleHAiOjE3MTI4Njk4NDh9.oVwNRruTk9-QOZx2rbJEKCjBMeaudOofMaOYHnU6vsk\",\n    \"expiresAt\": \"2024-04-11T21:10:48.702Z\"\n  }\n  ```", "tags": ["Authentication"], "security": [], "parameters": [], "requestBody": {"description": "the user's credentials", "required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthInput", "description": "the user's credentials"}}}}}}, "/opportunities/{opportunityId}": {"get": {"operationId": "GetOpportunity", "responses": {"200": {"description": "An opportunity object if found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Opportunity"}}}}}, "description": "Get a single opportunity by id", "tags": ["Opportunities"], "security": [{"bearerAuth": ["curator"]}], "parameters": [{"description": "the opportunity id", "in": "path", "name": "opportunityId", "required": true, "schema": {"type": "string"}}]}}, "/opportunities": {"post": {"operationId": "GetOpportunities", "responses": {"200": {"description": "the page of matching opportunities", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Page_Opportunity_"}}}}}, "description": "Query for a Page (set) of Opportunities with the supplied filter\n\nExample:\n\nGet a page of 100 opportunities starting at index 0 and omit results with a 'Deleted' status\nThe 'token' returned from the auth call must be present in the Authorization header.  The value is prefixed with \"Bearer \"\n\n```\ncurl -X POST https://api.soldierinnovation.com/opportunities \\\n-H 'Content-Type: application/json' \\\n-H \"Authorization: Bearer eyJhbGciO<token truncated for readability>\" \\\n-d '\n{\n  \"pagingInput\": { \"pageSize\": 100, \"cursor\": \"0\" },\n  \"searchSortInput\": {\n    \"searchFields\": [\n      {\"fieldNames\": [\"status\"], \"operator\": \"nin\", \"searchValue\": [\"Deleted\"]}\n    ]\n  }\n}\n'\n```\nResponse:\n```\n{\n\"results\": [\n    {\n        \"id\": \"00000000-0000-0000-0000-000000000002\",\n        \"lastCurated\": null,\n        \"title\": \"Synchronised radical open system\",\n        <full Opp here ...>\n    },\n    <next Opp here ...>,\n    <next Opp here ...>,\n],\n\"pageInfo\": {\n    \"totalCount\": 383,\n    \"retrievedCount\": 99,\n    \"hasNext\": true,\n    \"hasPrevious\": false,\n    \"lastCursor\": \"0\",\n    \"lastPageSize\": 100\n}\n}\n```\n\nExample:  \n\nSame as above example but queries across multiple Tenants' data\n\n\n```\ncurl -X POST https://api.soldierinnovation.com/opportunities \\\n-H 'Content-Type: application/json' \\\n-H \"Authorization: Bearer eyJhbGciOiJI<token truncated for readability>\" \\\n-d '\n{\n  \"pagingInput\": { \"pageSize\": 100, \"cursor\": \"0\" },\n  \"searchSortInput\": {\n    \"searchFields\": [\n      {\"fieldNames\": [\"status\"], \"operator\": \"nin\", \"searchValue\": [\"Deleted\"]}\n    ]\n  },\n  \"scope\": {\n    \"resources\": [\n      {\"resourceId\": \"00000000-0000-0000-0000-000000000001\", \"resourceType\": \"TENANT\" },\n      {\"resourceId\": \"00000000-0000-0000-0000-000000000002\", \"resourceType\": \"TENANT\" }\n    ]\n  }\n}\n'\n```\n\nResponse:   \n\n```\nSame as above results but will include both Tenants' data\n```\n<br>\n## (See the QueryInput object 'Schema' below for more examples)", "tags": ["Opportunities"], "security": [{"bearerAuth": ["curator"]}], "parameters": [], "requestBody": {"description": "the query input object", "required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/QueryInput", "description": "the query input object"}}}}}}, "/attachments/{attachmentId}": {"get": {"operationId": "GetAttachmentLocation", "responses": {"200": {"description": "the an object with the location (uri) of the attachment", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Location"}], "nullable": true}}}}}, "description": "Get the location (uri) of an attachment. These URLs are temporary and will expire after a short period of time", "tags": ["Attachments"], "security": [{"bearerAuth": ["curator"]}], "parameters": [{"description": "the unique identifier of the attachment", "in": "path", "name": "attachmentId", "required": true, "schema": {"type": "string"}}]}}}, "servers": [{"url": "/"}]}