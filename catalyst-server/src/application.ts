import {
  Connection,
  CreateRequestContext,
  DriverException,
  IDatabaseDriver,
  MikroORM,
  RequestContext,
} from '@mikro-orm/core';
import { authChecker } from 'core/auth/authUtils';
import { JwtPayload } from 'core/auth/JwtPayload';
import { TenantInfo } from 'core/contracts/output/TenantInfo';
import { OpportunityDownloadController } from 'core/controllers/curator/OpportunityDownloadController';
import { ProjectDownloadController } from 'core/controllers/curator/ProjectDownloadController';
import { AppContext } from 'core/core';
import { Tenant } from 'core/entities/Tenant';
import cors from 'cors';
import { Error<PERSON>and<PERSON> } from 'errorHandler';
import express, { Request, Response } from 'express';
import 'express-async-errors';
import { graphqlHTTP } from 'express-graphql';
import { expressjwt } from 'express-jwt';
import { GraphQLError, GraphQLSchema } from 'graphql';
import expressPlayground from 'graphql-playground-middleware-express';
import { graphqlUploadExpress } from 'graphql-upload';
import { Server } from 'http';
import 'init';
import { Secret } from 'jsonwebtoken';
import ormConfig from 'mikro-orm.config';
import { ApplicationMetaResolver } from 'resolvers/base/ApplicationMetaResolver';
import { PrivilegeResolver } from 'resolvers/base/PrivilegeResolver';
import { TenantAliasResolver } from 'resolvers/base/TenantAliasResolver';
import { TenantResolver } from 'resolvers/base/TenantResolver';
import { UserResolver as Base_UserResolver } from 'resolvers/base/UserResolver';
import { AttachmentResolver } from 'resolvers/curator/AttachmentResolver';
import { CategoryResolver } from 'resolvers/curator/CategoryResolver';
import { CurationEventResolver } from 'resolvers/curator/CurationEventResolver';
import { ExistingSolutionResolver } from 'resolvers/curator/ExistingSolutionResolver';
import { OwnerResolver } from 'resolvers/user/OwnerResolver';
import { OpportunityOwnerResolver } from 'resolvers/curator/OpportunityOwnerResolver';
import { OpportunityResolver as Curator_OpportunityResolver } from 'resolvers/curator/OpportunityResolver';
import { ProjectResolver } from 'resolvers/curator/ProjectResolver';
import { ReportResolver } from 'resolvers/curator/ReportResolver';
import { StakeholderResolver } from 'resolvers/curator/StakeholderResolver';
import { UserResolver as Curator_UserResolver } from 'resolvers/curator/UserResolver';
import { SubmissionResolver as User_SubmissionResolver } from 'resolvers/user/SubmissionResolver';
import { UserResolver as User_UserResolver } from 'resolvers/user/UserResolver';
import { buildSchema } from 'type-graphql';
import version from 'version.json';
import { LinkResolver } from 'resolvers/curator/LinkResolver';
import { RequirementResolver } from 'resolvers/curator/RequirementResolver';

export default class Application {
  public orm?: MikroORM<IDatabaseDriver<Connection>>;
  public app?: express.Application;
  public server?: Server;
  public tenantServerConfigs: Map<string, unknown> = new Map<string, unknown>();

  @CreateRequestContext()
  async getAllTenantServerConfigs(): Promise<Map<string, unknown>> {
    if (!this.orm?.em) return Promise.resolve(this.tenantServerConfigs);
    try {
      const allTenants = await this.orm?.em.getRepository(Tenant).findAll({ populate: ['id', 'meta'] });
      allTenants.map((tenant) => {
        if (tenant.meta && tenant.meta.serverConfig) {
          this.tenantServerConfigs.set(tenant.id, tenant.meta?.serverConfig);
        }
      });
    } catch (error) {
      const { code, name } = error as DriverException;
      if (code === '42P01') {
        // We are swallowing this error as it should only occur if we haven't bootstrapped the db or set the schema
        // const tableNotFoundExc = error as TableNotFoundException;
        console.info('Tenant table has not been loaded. Application context will not set serverConfig data.');
        return Promise.resolve(this.tenantServerConfigs);
      }
      throw `Error loading Tenant entity.\r\n${error}`;
    }
    return Promise.resolve(this.tenantServerConfigs);
  }

  public init = async (): Promise<void> => {
    try {
      this.orm = await MikroORM.init(ormConfig);
      this.tenantServerConfigs = await this.getAllTenantServerConfigs();
    } catch (error) {
      console.error('📌 Could not connect to the database', error);
      throw Error('');
    }
    TenantInfo.serverVersion = version?.serverVersion;
    this.registerShutdown();
  };

  public start = async (): Promise<void> => {
    this.app = express();

    /*
    this.app.use(function (req, res, next) {
      const start = Date.now();
      res.on('finish', function () {
        const duration = Date.now() - start;
        console.log(`perf: ${JSON.stringify(req.body)?.substring(10, 30)} took: ${duration}ms`);
      });
      next();
    });
    */

    // Jwt Setup
    this.app.use(
      expressjwt({
        secret: process.env.JWT_SECRET as Secret,
        algorithms: ['HS256'],
        credentialsRequired: false,
      }),
    );

    //NOTE: When moving to sessions, middleware for graphql path will go here, and decode the session
    // if it is available. It can then be used in resolvers that require auth.
    // Some resolvers will not require a session

    // Graphql playground in dev mode
    if (process.env.NODE_ENV !== 'production') {
      this.app.get('/user', expressPlayground({ endpoint: '/user' }));
      this.app.get('/curator', expressPlayground({ endpoint: '/curator' }));
    }
    if (process.env.EP_ENDPOINT)
      this.app.get(`/${process.env.EP_ENDPOINT}`, expressPlayground({ endpoint: '/curator' }));

    this.app.use(cors());

    /***
     *      _____              __        __  ____        __          _      __
     *     / ___/______ ____  / /  ___ _/ / / __/__  ___/ /__  ___  (_)__  / /____
     *    / (_ / __/ _ `/ _ \/ _ \/ _ `/ / / _// _ \/ _  / _ \/ _ \/ / _ \/ __(_-<
     *    \___/_/  \_,_/ .__/_//_/\_, /_/ /___/_//_/\_,_/ .__/\___/_/_//_/\__/___/
     *                /_/          /_/                 /_/
     */

    // set up user endpoint
    try {
      const userSchema: GraphQLSchema = await buildSchema({
        resolvers: [Base_UserResolver, User_UserResolver, User_SubmissionResolver, TenantResolver],
        authChecker: authChecker,
        dateScalarMode: 'isoDate',
        emitSchemaFile: {
          path: './user.gql',
          sortedSchema: false,
        },
      });

      this.app.use((req, res, next) => {
        if (!this.orm) return next();
        const token = (<any>req)?.auth as JwtPayload;
        const loggerCtx = {
          user: { userId: token?._1, tenantId: token?._2, roles: token?._3 },
          tenantConfig: this.tenantServerConfigs.get(token?._2),
        };

        const em = this.orm.em.fork({
          useContext: true,
          loggerContext: loggerCtx,
        });

        RequestContext.create(em, next);
      });

      this.app.post(
        '/user',
        express.json(),
        graphqlHTTP((req, res) => {
          const context = {
            req,
            res,
            em: this.orm?.em.fork(),
            token: (<any>req)?.auth as JwtPayload,
            serverConfigs: this.tenantServerConfigs,
          } as AppContext;
          res.on('finish', () => {
            context.em.clear();
          });
          return {
            schema: userSchema,
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            context,
            customFormatErrorFn: (error: GraphQLError) => {
              console.error(error.stack);
              throw error;
            },
          };
        }),
      );
      // set up curator endpoint
      const curatorSchema: GraphQLSchema = await buildSchema({
        resolvers: [
          Base_UserResolver,
          Curator_UserResolver,
          Curator_OpportunityResolver,
          ProjectResolver,
          CategoryResolver,
          StakeholderResolver,
          AttachmentResolver,
          CurationEventResolver,
          TenantAliasResolver,
          TenantResolver,
          ApplicationMetaResolver,
          PrivilegeResolver,
          ReportResolver,
          OwnerResolver,
          OpportunityOwnerResolver,
          ExistingSolutionResolver,
          RequirementResolver,
          LinkResolver,
        ],
        authChecker: authChecker,
        dateScalarMode: 'isoDate',
        emitSchemaFile: {
          path: './curator.gql',
          sortedSchema: false,
        },
      });

      this.app.post(
        '/curator',
        // file upload - client should use https://formidable.com/open-source/urql/docs/api/multipart-fetch-exchange/
        graphqlUploadExpress({ maxFileSize: 10000000, maxFiles: 5 }),
        express.json(),
        graphqlHTTP((req, res) => {
          const context = {
            req,
            res,
            em: this.orm?.em.fork(),
            token: (<any>req)?.auth as JwtPayload,
            serverConfigs: this.tenantServerConfigs,
          } as AppContext;
          res.on('finish', () => {
            context.em.clear();
          });
          return {
            schema: curatorSchema,
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            context,
            customFormatErrorFn: (error) => {
              console.error(error.stack);
              throw error;
            },
          };
        }),
      );

      /***
       *       _____ __      ___                  __             __
       *      / __(_) /__   / _ \___ _    _____  / /__  ___ ____/ /
       *     / _// / / -_) / // / _ \ |/|/ / _ \/ / _ \/ _ `/ _  /
       *    /_/ /_/_/\__/ /____/\___/__,__/_//_/_/\___/\_,_/\_,_/
       *
       */

      // file download
      // @TODO move this logic to a controller!
      this.app.post('/ic/download/:type/:id?', express.json(), async (req, res) => {
        const { type, id } = req.params;
        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        res.setHeader('Content-Disposition', `attachment; filename=innovation_${type}.xlsx`);
        res.status(200);
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const ctx = {
          req,
          res,
          em: this.orm?.em.fork(),
          token: (<any>req)?.auth as JwtPayload,
          serverConfigs: this.tenantServerConfigs,
        } as AppContext;
        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
        if (type === 'project') {
          await ProjectDownloadController.downloadProjects(ctx, id);
        } else {
          await OpportunityDownloadController.downloadOpportunities(ctx, id);
        }
      });

      /***
       *       ______       __  _       ___
       *      / __/ /____ _/ /_(_)___  / _ \___ ____ ____ ___
       *     _\ \/ __/ _ `/ __/ / __/ / ___/ _ `/ _ `/ -_|_-<
       *    /___/\__/\_,_/\__/_/\__/ /_/   \_,_/\_, /\__/___/
       *                                       /___/
       */

      this.app.use(express.static(__dirname + '/web'));

      // user
      this.app.get(
        ['/', '/tenant/*', '/sAuth', '/sAuth/*', '/sMain', '/sMain/*', '/InnovationCenter', '/InnovationCenter/*'],
        (req: Request, res: Response) => {
          res.sendFile(__dirname + '/web/index.html');
        },
      );

      // curator
      this.app.get(
        [
          '/ic',
          '/ic/tenant/*',
          '/cAuth',
          '/cAuth/*',
          '/cMain',
          '/cMain/*',
          '/InnovationAdministrator',
          '/InnovationAdministrator/*',
        ],
        (req: Request, res: Response) => {
          res.sendFile(__dirname + '/web/ic/index.html');
        },
      );

      /***
       *       ____                       _____          ____
       *      / __/__ _____  _____ ____  / ___/__  ___  / _(_)__ _
       *     _\ \/ -_) __/ |/ / -_) __/ / /__/ _ \/ _ \/ _/ / _ `/
       *    /___/\__/_/  |___/\__/_/    \___/\___/_//_/_//_/\_, /
       *                                                   /___/
       */

      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      this.app.use((error: Error, req: express.Request, res: express.Response, next: express.NextFunction): void => {
        console.error('📌 Something went wrong', error);
        const errorResult = ErrorHandler.handleError(error);
        res.status(errorResult.statusCode).send({ errors: errorResult.errors });
      });

      const port = process.env.PORT || 4000;
      this.server = this.app.listen(port as number, '0.0.0.0', () => {
        // NOTE: If you are having issues with the playground tooltips not going away, this is a fast way to
        // remove them from the console under the browser developer tools:
        //
        // document.querySelectorAll('.CodeMirror-hint-information').forEach(node => node.remove());
        //
        // Just past that in the console window inside the developer tools and it will clear the tooltips
        console.log(`🚀 Server version: ${TenantInfo.serverVersion}`);
        if (process.env.NODE_ENV !== 'production') {
          console.log(`🚀 http://localhost:${port}/user`);
          console.log(`🚀 http://localhost:${port}/curator`);
        }
      });
    } catch (error) {
      console.error('📌 Could not start server', error);
    }
  };

  private registerShutdown() {
    /***
     *       ____                       _______
     *      / __/__ _____  _____ ____  / ___/ /__ ___ ____  __ _____
     *     _\ \/ -_) __/ |/ / -_) __/ / /__/ / -_) _ `/ _ \/ // / _ \
     *    /___/\__/_/  |___/\__/_/    \___/_/\__/\_,_/_//_/\_,_/ .__/
     *                                                        /_/
     */
    const server = this.server;
    const orm = this.orm;

    // quit on ctrl-c when running docker in terminal
    process.on('SIGINT', function onSigint() {
      console.info('Got SIGINT (aka ctrl-c ). Waiting for shutdown...', new Date().toISOString());
      shutdown();
    });

    // quit properly on docker stop
    process.on('SIGTERM', function onSigterm() {
      console.info('Got SIGTERM (docker container stop). Graceful shutdown ', new Date().toISOString());
      shutdown();
    });

    // shut down server
    function shutdown() {
      disconnectAll().then((results) => {
        if (results.some((result) => result.status === 'rejected')) {
          results.forEach((result) => {
            if (result.status === 'rejected') console.error(`Shutdown error:`, result.reason);
          });
          process.exit(1);
        }
        console.info('Shutdown completed successfully.', new Date().toISOString());
        process.exit(0);
      });
    }

    async function disconnectAll(): Promise<[PromiseSettledResult<void>, PromiseSettledResult<void>]> {
      return Promise.allSettled([shutdownServer(), orm ? orm.close() : Promise.resolve()]);
    }

    async function shutdownServer() {
      if (!server) return Promise.resolve();
      return new Promise<void>((resolve, reject) => {
        server?.close((err) => {
          err ? reject(err) : resolve();
        });
      });
    }
  }
}
