import { Connection, EntityManager, IDatabaseDriver } from '@mikro-orm/core';
import { errorKeys } from 'core/contracts/errors/ErrorCodes';
import { CreateRequirementInput, UpdateRequirementInput } from 'core/contracts/input/curator/RequirementInput';
import { Requirement } from 'core/entities/Requirement';
import { Opportunity } from 'core/entities/Opportunity';

export class RequirementController {
  public static async createRequirement(params: {
    em: EntityManager<IDatabaseDriver<Connection>>;
    input: CreateRequirementInput;
    opportunityId: string;
    tenantId: string;
    relationPaths?: string[];
  }): Promise<Requirement> {
    const { em, input, opportunityId, tenantId, relationPaths = [] } = params;

    // Verify the opportunity exists and belongs to the tenant
    const opportunity = await em.getRepository(Opportunity).findOne({
      id: opportunityId,
      tenant: { id: tenantId },
    });

    if (!opportunity) throw new Error(errorKeys.OBJECT_NOT_FOUND);

    const requirement = Requirement.newRequirement({
      ...input,
      opportunity,
    });

    await em.persist(requirement).flush();

    if (relationPaths.length > 0) {
      await em.populate(requirement, relationPaths as never);
    }

    return requirement;
  }

  public static async getRequirement(params: {
    em: EntityManager<IDatabaseDriver<Connection>>;
    id: string;
    tenantId: string;
    relationPaths?: string[];
  }): Promise<Requirement | null> {
    const { em, id, tenantId, relationPaths = [] } = params;

    return em.getRepository(Requirement).findOne(
      {
        id,
        opportunity: { tenant: { id: tenantId } },
      },
      { populate: relationPaths as never },
    );
  }

  public static async getRequirementsByOpportunity(params: {
    em: EntityManager<IDatabaseDriver<Connection>>;
    opportunityId: string;
    tenantId: string;
    relationPaths?: string[];
  }): Promise<Requirement[]> {
    const { em, opportunityId, tenantId, relationPaths = [] } = params;

    return em.getRepository(Requirement).find(
      {
        opportunity: {
          id: opportunityId,
          tenant: { id: tenantId },
        },
      },
      { populate: relationPaths as never },
    );
  }

  public static async updateRequirement(params: {
    em: EntityManager<IDatabaseDriver<Connection>>;
    id: string;
    input: UpdateRequirementInput;
    tenantId: string;
    relationPaths?: string[];
  }): Promise<Requirement> {
    const { em, id, input, tenantId, relationPaths = [] } = params;

    const requirement = await em.getRepository(Requirement).findOneOrFail({ id }, { populate: relationPaths as never });

    requirement.modify(input);
    await em.persist(requirement).flush();

    await em.populate(requirement, relationPaths as never);

    return requirement;
  }

  public static async deleteRequirement(params: {
    em: EntityManager<IDatabaseDriver<Connection>>;
    id: string;
    tenantId: string;
  }): Promise<boolean> {
    const { em, id, tenantId } = params;

    const requirement = await em.getRepository(Requirement).findOne({
      id,
      opportunity: { tenant: { id: tenantId } },
    });

    if (!requirement) throw new Error(errorKeys.OBJECT_NOT_FOUND);

    await em.remove(requirement).flush();
    return true;
  }
}
