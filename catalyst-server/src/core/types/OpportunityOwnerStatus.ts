import { OpportunityOwnerStatus as _OpportunityOwnerStatus } from 'core/entities/OpportunityOwnerStatus';
import { Owner } from './Owner';
import { Opportunity } from './Opportunity';
/**
 * A user of the system
 */
export interface OpportunityOwnerStatus
  extends Pick<
    _OpportunityOwnerStatus,
    | 'id'
    | 'status'
    | 'statusSetPreviousAt'
    | 'statusSetRemovedAt'
    | 'isRemoved'
  > {
    owner: Owner;
    opportunity: Opportunity; // Assuming this is the full Opportunity type
  }
