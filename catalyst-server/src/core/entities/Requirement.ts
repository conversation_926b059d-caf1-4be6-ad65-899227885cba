import { Entity, ManyToOne, Property } from '@mikro-orm/core';
import { Field, ObjectType } from 'type-graphql';
import { Exclude, Expose } from 'class-transformer';
import { CoreEntity } from './CoreEntity';
import { Opportunity } from './Opportunity';

@ObjectType({ simpleResolvers: true })
@Entity()
@Exclude()
export class Requirement extends CoreEntity {
  @Field()
  @Property()
  @Expose()
  source!: string;

  @Field({ nullable: true })
  @Property({ nullable: true })
  @Expose()
  title?: string;

  @Field()
  @Property()
  @Expose()
  poc!: string;

  @ManyToOne(() => Opportunity)
  @Field(() => Opportunity)
  opportunity!: Opportunity;

  static newRequirement(values: Partial<Requirement>): Requirement {
    const instance = new Requirement();
    instance.initialize(values);
    return instance;
  }
}
