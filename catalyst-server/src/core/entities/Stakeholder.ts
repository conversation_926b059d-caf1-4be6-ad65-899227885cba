import {
  BeforeCreate,
  BeforeUpdate,
  Cascade,
  Collection,
  Entity,
  EntityManager,
  ManyToMany,
  ManyToOne,
  OneToMany,
  OnLoad,
  Property,
  // Unique,
  ChangeSet,
  EntityMetadata,
} from '@mikro-orm/core';
import { Field, ObjectType } from 'type-graphql';
import { CoreEntity } from './CoreEntity';
import { Opportunity } from './Opportunity';
import { ProjectStakeholder } from './ProjectStakeholder';
import { Tenant } from './Tenant';
import { Exclude, Expose } from 'class-transformer';

@ObjectType({ simpleResolvers: true })
@Entity()
@Exclude()
// @Unique({ properties: ['name', 'org', 'tenant'] })
export class Stakeholder extends CoreEntity {
  @Field({ nullable: true })
  @Property({ nullable: true })
  @Expose()
  name?: string;

  @Field({ nullable: true })
  @Property({ nullable: true })
  @Expose()
  firstName?: string;

  @Field({ nullable: true })
  @Property({ nullable: true })
  @Expose()
  lastName?: string;

  @Field({ nullable: true })
  @Property({ nullable: true })
  @Expose()
  title?: string;

  @Field({ nullable: true })
  @Property({ nullable: true })
  @Expose()
  phone?: string;

  @Field({ nullable: true })
  @Property({ nullable: true })
  @Expose()
  emailAddress?: string;

  @Field({ nullable: true })
  @Property({ nullable: true })
  @Expose()
  altEmailAddress?: string;

  @Field({ nullable: true })
  @Property({ nullable: true })
  @Expose()
  org?: string;

  @Field({ nullable: true })
  @Property({ nullable: true })
  @Expose()
  organizationRole?: string;

  @ManyToOne(() => Tenant, { deleteRule: 'cascade' })
  tenant!: Tenant;
  @Field(() => [Opportunity])
  @ManyToMany(() => Opportunity, (o: Opportunity) => o.stakeholders)
  opportunities = new Collection<Opportunity>(this);

  @OneToMany(() => ProjectStakeholder, (p: ProjectStakeholder) => p.stakeholder, {
    orphanRemoval: true,
    cascade: [Cascade.ALL],
  })
  projectStakeholders = new Collection<ProjectStakeholder>(this);

  async removeAllAssociations(): Promise<void> {
    await this.projectStakeholders.init();
    this.projectStakeholders.removeAll();
  }

  // This hook is called after the entity is loaded from the database.
  @OnLoad()
  populateDataFromLegacyName() {
    // If the new fields are empty but the legacy one has a value,
    // we use it to populate the new fields firstName and lastName.
    if (this.name && !this.firstName && !this.lastName) {
      // Fallback: If only 'name' is being updated (e.g., in legacy code),
      // update firstName/lastName from it. This is for the transition phase.
      const parts = this.name.split(' ');
      this.firstName = parts[0];
      this.lastName = parts.slice(1).join(' ');
    } else {
      const parts = [this.firstName, this.lastName].filter(Boolean);
      this.name = parts.join(' '); // Keep name in sync
    }
  }

  // Update logic to ensure firstName/lastName are prioritized
  @BeforeCreate()
  @BeforeUpdate()
  migrateNameValue(args: {
    changeSet: ChangeSet<Stakeholder>;
    entity: Stakeholder;
    em: EntityManager;
    meta: EntityMetadata<Stakeholder>;
  }) {
    const { payload } = args.changeSet;

    // For now, we want to keep name and the addition of firstName/lastName in sync.
    // This is a temporary solution until we fully migrate to the new fields.
    // If the new fields are empty but the legacy one has a value,
    // we use it to populate the new fields.
    if (payload.firstName || payload.lastName) {
      if (this.firstName || this.lastName) {
        const parts = [this.firstName, this.lastName].filter(Boolean);
        this.name = parts.join(' ');
      } else {
        // If both are cleared, clear name.
        this.name = undefined;
      }
    }
    // If Only the name field was updated. We use it to update `firstName` and `lastName`.
    // This condition prevents an infinite loop, as `name` will be marked as dirty in the first if.
    else if (payload.name) {
      if (this.name) {
        const parts = this.name.split(' ');
        this.firstName = parts[0];
        this.lastName = parts.slice(1).join(' ') || undefined;
      } else {
        // If name is cleared, clear the name props.
        this.firstName = undefined;
        this.lastName = undefined;
      }
    }
  }

  static newStakeholder(values: Partial<Stakeholder>): Stakeholder {
    const instance = new Stakeholder();
    instance.initialize(values);
    return instance;
  }
}
