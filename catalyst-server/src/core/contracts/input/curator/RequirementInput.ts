import { IsNotEmpty } from 'class-validator';
import { Requirement } from 'core/entities/Requirement';
import { Field, InputType } from 'type-graphql';

@InputType()
export class CreateRequirementInput implements Partial<Requirement> {
  @Field()
  @IsNotEmpty({ message: 'Source is required' })
  source!: string;

  @Field({ nullable: true })
  title?: string;

  @Field()
  @IsNotEmpty({ message: 'POC is required' })
  poc!: string;
}

@InputType()
export class UpdateRequirementInput implements Partial<CreateRequirementInput> {
  @Field({ nullable: true })
  source?: string;

  @Field({ nullable: true })
  title?: string;

  @Field({ nullable: true })
  poc?: string;
}

@InputType()
export class RequirementLinks {
  @Field()
  @IsNotEmpty({ message: 'Opportunity ID is required' })
  opportunityId!: string;
}
