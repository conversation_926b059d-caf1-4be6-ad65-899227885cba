import { Stakeholder } from 'core/entities/Stakeholder';
import { Field, InputType } from 'type-graphql';

@InputType()
export class CreateStakeholderInput implements Partial<Stakeholder> {
  // @Field({ nullable: true })
  // name?: string;
  @Field({ nullable: true })
  firstName?: string;
  @Field({ nullable: true })
  lastName?: string;
  @Field({ nullable: true })
  title?: string;
  @Field({ nullable: true })
  phone?: string;
  @Field({ nullable: true })
  emailAddress?: string;
  @Field({ nullable: true })
  altEmailAddress?: string;
  @Field({ nullable: true })
  org?: string;
  @Field({ nullable: true })
  organizationRole?: string;
}

@InputType()
export class UpdateStakeholderInput implements Partial<CreateStakeholderInput> {
  // @Field({ nullable: true })
  // name?: string;
  @Field({ nullable: true })
  firstName?: string;
  @Field({ nullable: true })
  lastName?: string;
  @Field({ nullable: true })
  title?: string;
  @Field({ nullable: true })
  phone?: string;
  @Field({ nullable: true })
  emailAddress?: string;
  @Field({ nullable: true })
  altEmailAddress?: string;
  @Field({ nullable: true })
  org?: string;
  @Field({ nullable: true })
  organizationRole?: string;
}
