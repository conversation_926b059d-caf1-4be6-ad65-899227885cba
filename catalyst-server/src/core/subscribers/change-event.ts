import { ChangeSet, EventSubscriber, FlushEventArgs } from '@mikro-orm/core';
import { ChangeEventType } from 'core/contracts/enums/ChangeEventType';
import { TargetType } from 'core/contracts/enums/TargetType';
import { ChangeEvent } from 'core/entities/ChangeEvent';
import fs from 'fs';
import path from 'path';

export type Condition = {
  field: string;
  from?: unknown; // Previous value to match (optional)
  to?: unknown; // New value to match (optional)
  operator: 'eq';
};

export type ConditionGroup = {
  operator: 'and' | 'or';
  conditions: Array<Condition>;
};

export type EventTrackingConfig =
  | {
      eventType: ChangeEventType;
      op: 'update';
      entityType: string;
      fields: Array<string>; // Only track changes to these specific fields
      conditionGroups?: Array<ConditionGroup>; // Optional filtering conditions
    }
  | {
      eventType: ChangeEventType;
      op: 'create' | 'delete';
      entityType: string;
    };

const CONFIG_PATH = path.resolve(__dirname, '../../../config/audit-logs/audit-logs.json');

// Cache for loaded configuration to avoid repeated file reads
let configCache: Record<string, Array<EventTrackingConfig>> = {};

function loadEventTrackingConfig(): Record<string, Array<EventTrackingConfig>> {
  try {
    const raw = fs.readFileSync(CONFIG_PATH, 'utf-8');
    configCache = JSON.parse(raw);
  } catch (e) {
    console.error('Failed to load change-events config:', e);
    configCache = {};
  }
  return configCache;
}

function getConfigsForEntity(entity: ChangeSet<Partial<unknown>>): EventTrackingConfig[] {
  const entityName = entity.name;
  return configCache[entityName] ?? [];
}

// Load configuration on module initialization
loadEventTrackingConfig();

function evaluateCondition(
  cond: Condition,
  original: Record<string, unknown> | undefined,
  payload: Record<string, unknown>,
): boolean {
  const prev = original?.[cond.field];
  const next = payload[cond.field];

  // Skip if neither value exists (no meaningful change)
  if (!prev && !next) return false;

  switch (cond.operator) {
    case 'eq':
      // Check if the 'from' and 'to' values match the actual change
      const fromMatches = cond.from === undefined || cond.from === prev;
      const toMatches = cond.to === undefined || cond.to === next;
      return fromMatches && toMatches;
    default:
      console.warn(`Unsupported operator: ${cond.operator}`);
      return false;
  }
}

function evaluateConditionGroups(
  conditionGroups: Array<ConditionGroup>,
  original: Record<string, unknown> | undefined,
  payload: Record<string, unknown>,
): boolean {
  for (const group of conditionGroups) {
    const results = group.conditions.map((cond) => evaluateCondition(cond, original, payload));

    if (group.operator === 'and') {
      // All conditions must be true for AND groups
      if (results.every(Boolean)) return true;
    } else if (group.operator === 'or') {
      // At least one condition must be true for OR groups
      if (results.some(Boolean)) return true;
    }
  }

  return false;
}

export class ChangeEventSubscriber implements EventSubscriber {
  async onFlush(args: FlushEventArgs) {
    const em = args.em.fork();
    const uow = args.em.getUnitOfWork();
    const logs: Array<ChangeEvent> = [];
    const emLoggerContext = args.em.getLoggerContext();
    // Extract the user ID from the logger context for audit trail
    const actorUserId = emLoggerContext?.user?.userId ?? null;

    for (const changeSet of uow.getChangeSets()) {
      const entity = changeSet.entity;
      // Skip if no entity or if it's a ChangeEvent itself (for quick exit)
      if (!entity || entity instanceof ChangeEvent) continue;

      const configs = getConfigsForEntity(changeSet);
      if (!configs.length) continue;

      for (const config of configs) {
        if (config.op !== changeSet.type) continue;

        let shouldLog = true;

        if (config.op === 'update') {
          // For updates, only log if the changed fields intersect with configured fields
          const changedFields = Object.keys(changeSet.payload);
          const intersects = config.fields.some((f) => changedFields.includes(f));
          shouldLog = intersects;

          // Apply additional filtering conditions if configured
          if (shouldLog && 'conditionGroups' in config && config.conditionGroups) {
            shouldLog = evaluateConditionGroups(config.conditionGroups, changeSet.originalEntity, changeSet.payload);
          }
        }

        if (!shouldLog) continue;

        // Create the change event log entry
        logs.push(
          em.create(ChangeEvent, {
            eventType: config.eventType,
            user: actorUserId ? em.getReference('User', actorUserId) : undefined,
            targetId: entity.id,
            targetType: config.entityType as TargetType,
            payload: changeSet.payload,
            createdAt: new Date(),
          }),
        );
      }
    }

    // Persist all collected change events
    for (const log of logs) {
      try {
        await em.persist(log).flush();
      } catch (e) {
        console.error('Failed to persist change event:', e);
      }
    }
  }
}
