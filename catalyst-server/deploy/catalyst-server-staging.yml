apiVersion: apps/v1
kind: Deployment
metadata:
  name: catalyst-server-staging
spec:
  replicas: 1
  selector:
    matchLabels:
      app: catalyst-server-staging
  strategy: {}
  template:
    metadata:
      labels:
        app: catalyst-server-staging
    spec:
      nodeSelector:
        "kubernetes.io/os": linux
      containers:
      - name: catalyst-server
        image: catalystinnovation.azurecr.us/catalyst-server:1.29.0
        envFrom:
        - secretRef:
            name: staging-secrets
        imagePullPolicy: Always
        ports:
        - containerPort: 8080
          name: catalyst-server
        resources: {}
      restartPolicy: Always
status: {}

---

apiVersion: v1
kind: Service
metadata:
  name: catalyst-server-staging
spec:
  ports:
  - port: 8080
  selector:
    app: catalyst-server-staging
