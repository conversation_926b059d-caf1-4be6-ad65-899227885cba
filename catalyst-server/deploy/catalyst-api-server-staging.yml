apiVersion: apps/v1
kind: Deployment
metadata:
  name: catalyst-api-server-staging
spec:
  replicas: 1
  selector:
    matchLabels:
      app: catalyst-api-server-staging
  strategy: {}
  template:
    metadata:
      labels:
        app: catalyst-api-server-staging
    spec:
      nodeSelector:
        "kubernetes.io/os": linux
      containers:
      - name: catalyst-api-server
        image: catalystinnovation.azurecr.us/catalyst-api-server:1.29.0
        envFrom:
        - secretRef:
            name: staging-secrets
        imagePullPolicy: Always
        ports:
        - containerPort: 8080
          name: api-server
        resources: {}
      restartPolicy: Always
status: {}

---

apiVersion: v1
kind: Service
metadata:
  name: catalyst-api-server-staging
spec:
  ports:
  - port: 8080
  selector:
    app: catalyst-api-server-staging