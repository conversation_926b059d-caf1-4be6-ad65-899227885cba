import { Migration } from '@mikro-orm/migrations';

export class Migration20250729185914 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`create table "owner" ("id" uuid not null, "created_at" timestamptz not null, "updated_at" timestamptz null, "organization_role" varchar(255) null, "user_id" uuid not null, "tenant_id" uuid not null, constraint "owner_pkey" primary key ("id"));`);
    this.addSql(`alter table "owner" add constraint "owner_user_id_unique" unique ("user_id");`);

    this.addSql(`create table "requirement" ("id" uuid not null, "created_at" timestamptz not null, "updated_at" timestamptz null, "source" varchar(255) not null, "title" varchar(255) null, "poc" varchar(255) not null, "opportunity_id" uuid not null, constraint "requirement_pkey" primary key ("id"));`);

    this.addSql(`create table "opportunity_owner_status" ("id" uuid not null, "created_at" timestamptz not null, "updated_at" timestamptz null, "status" text check ("status" in ('Current', 'Previous', 'Initial', 'Removed')) not null default 'Current', "status_set_previous_at" timestamptz null, "status_set_removed_at" timestamptz null, "owner_id" uuid null, "opportunity_id" uuid not null, constraint "opportunity_owner_status_pkey" primary key ("id"));`);

    this.addSql(`create table "existing_solution" ("id" uuid not null, "created_at" timestamptz not null, "updated_at" timestamptz null, "source" varchar(255) not null, "title" varchar(255) null, "organization" varchar(255) null, "needs_modification" boolean not null default false, "opportunity_id" uuid not null, constraint "existing_solution_pkey" primary key ("id"));`);

    this.addSql(`create table "link" ("id" uuid not null, "created_at" timestamptz not null, "updated_at" timestamptz null, "name" varchar(255) not null, "url" varchar(255) not null, "notes" varchar(255) null, "created_by_id" uuid null, "opportunity_id" uuid null, constraint "link_pkey" primary key ("id"));`);

    this.addSql(`create table "change_event" ("id" uuid not null, "created_at" timestamptz not null, "updated_at" timestamptz null, "event_type" text check ("event_type" in ('user.passwordChange', 'user.creation', 'user.deletion')) not null, "user_id" uuid null, "payload" jsonb not null, "target_id" varchar(255) not null, "target_type" text check ("target_type" in ('Attachment', 'ApplicationMeta', 'Category', 'Opportunity', 'Privilege', 'PrivilegeGroup', 'Project', 'Role', 'Stakeholder', 'Submission', 'Tenant', 'TenantAlias', 'TenantMeta', 'User', 'UserOptions')) not null, constraint "change_event_pkey" primary key ("id"));`);
    this.addSql(`create index "change_event_target_type_target_id_index" on "change_event" ("target_type", "target_id");`);
    this.addSql(`create index "change_event_event_type_target_id_index" on "change_event" ("event_type", "target_id");`);

    this.addSql(`alter table "owner" add constraint "owner_user_id_foreign" foreign key ("user_id") references "user" ("id") on update cascade on delete cascade;`);
    this.addSql(`alter table "owner" add constraint "owner_tenant_id_foreign" foreign key ("tenant_id") references "tenant" ("id") on update cascade;`);

    this.addSql(`alter table "requirement" add constraint "requirement_opportunity_id_foreign" foreign key ("opportunity_id") references "opportunity" ("id") on update cascade;`);

    this.addSql(`alter table "opportunity_owner_status" add constraint "opportunity_owner_status_owner_id_foreign" foreign key ("owner_id") references "owner" ("id") on update cascade on delete set null;`);
    this.addSql(`alter table "opportunity_owner_status" add constraint "opportunity_owner_status_opportunity_id_foreign" foreign key ("opportunity_id") references "opportunity" ("id") on update cascade;`);

    this.addSql(`alter table "existing_solution" add constraint "existing_solution_opportunity_id_foreign" foreign key ("opportunity_id") references "opportunity" ("id") on update cascade;`);

    this.addSql(`alter table "link" add constraint "link_created_by_id_foreign" foreign key ("created_by_id") references "user" ("id") on update cascade on delete set null;`);
    this.addSql(`alter table "link" add constraint "link_opportunity_id_foreign" foreign key ("opportunity_id") references "opportunity" ("id") on update cascade on delete set null;`);

    this.addSql(`alter table "change_event" add constraint "change_event_user_id_foreign" foreign key ("user_id") references "user" ("id") on update cascade on delete set null;`);

    this.addSql(`alter table "role" drop constraint if exists "role_name_check";`);

    this.addSql(`alter table "project_stakeholder" drop constraint if exists "project_stakeholder_type_check";`);

    this.addSql(`alter table "curation_event" drop constraint if exists "curation_event_entity_type_check";`);

    this.addSql(`alter table "role" alter column "created_at" type timestamptz using ("created_at"::timestamptz);`);
    this.addSql(`alter table "role" alter column "updated_at" type timestamptz using ("updated_at"::timestamptz);`);
    this.addSql(`alter table "role" add constraint "role_name_check" check("name" in ('curator', 'admin', 'analyst', 'owner'));`);

    this.addSql(`alter table "tenant_meta" alter column "created_at" type timestamptz using ("created_at"::timestamptz);`);
    this.addSql(`alter table "tenant_meta" alter column "updated_at" type timestamptz using ("updated_at"::timestamptz);`);

    this.addSql(`alter table "tenant" alter column "created_at" type timestamptz using ("created_at"::timestamptz);`);
    this.addSql(`alter table "tenant" alter column "updated_at" type timestamptz using ("updated_at"::timestamptz);`);

    this.addSql(`alter table "tenant_alias" alter column "created_at" type timestamptz using ("created_at"::timestamptz);`);
    this.addSql(`alter table "tenant_alias" alter column "updated_at" type timestamptz using ("updated_at"::timestamptz);`);

    this.addSql(`alter table "stakeholder" drop constraint "stakeholder_name_org_tenant_id_unique";`);

    this.addSql(`alter table "stakeholder" add column "first_name" varchar(255) null, add column "last_name" varchar(255) null, add column "title" varchar(255) null, add column "phone" varchar(255) null, add column "email_address" varchar(255) null, add column "alt_email_address" varchar(255) null, add column "organization_role" varchar(255) null;`);
    this.addSql(`alter table "stakeholder" alter column "created_at" type timestamptz using ("created_at"::timestamptz);`);
    this.addSql(`alter table "stakeholder" alter column "updated_at" type timestamptz using ("updated_at"::timestamptz);`);

    this.addSql(`alter table "privilege_group" alter column "created_at" type timestamptz using ("created_at"::timestamptz);`);
    this.addSql(`alter table "privilege_group" alter column "updated_at" type timestamptz using ("updated_at"::timestamptz);`);

    this.addSql(`alter table "privilege" alter column "created_at" type timestamptz using ("created_at"::timestamptz);`);
    this.addSql(`alter table "privilege" alter column "updated_at" type timestamptz using ("updated_at"::timestamptz);`);

    this.addSql(`alter table "category" alter column "created_at" type timestamptz using ("created_at"::timestamptz);`);
    this.addSql(`alter table "category" alter column "updated_at" type timestamptz using ("updated_at"::timestamptz);`);

    this.addSql(`alter table "application_meta" alter column "created_at" type timestamptz using ("created_at"::timestamptz);`);
    this.addSql(`alter table "application_meta" alter column "updated_at" type timestamptz using ("updated_at"::timestamptz);`);

    this.addSql(`alter table "user" alter column "created_at" type timestamptz using ("created_at"::timestamptz);`);
    this.addSql(`alter table "user" alter column "updated_at" type timestamptz using ("updated_at"::timestamptz);`);

    this.addSql(`alter table "submission" alter column "created_at" type timestamptz using ("created_at"::timestamptz);`);
    this.addSql(`alter table "submission" alter column "updated_at" type timestamptz using ("updated_at"::timestamptz);`);

    this.addSql(`alter table "project" alter column "created_at" type timestamptz using ("created_at"::timestamptz);`);
    this.addSql(`alter table "project" alter column "updated_at" type timestamptz using ("updated_at"::timestamptz);`);
    this.addSql(`alter table "project" alter column "start_date" type timestamptz using ("start_date"::timestamptz);`);
    this.addSql(`alter table "project" alter column "end_date" type timestamptz using ("end_date"::timestamptz);`);
    this.addSql(`alter table "project" alter column "last_curated" type timestamptz using ("last_curated"::timestamptz);`);

    this.addSql(`alter table "project_stakeholder" alter column "created_at" type timestamptz using ("created_at"::timestamptz);`);
    this.addSql(`alter table "project_stakeholder" alter column "updated_at" type timestamptz using ("updated_at"::timestamptz);`);
    this.addSql(`alter table "project_stakeholder" add constraint "project_stakeholder_type_check" check("type" in ('division', 'performer', 'transition', 'uncategorized'));`);

    this.addSql(`alter table "opportunity" add column "is_ti_cloe" boolean not null default false, add column "army_modernization_priority" varchar(255) null, add column "echelon_applicability" varchar(255) null, add column "transition_in_contact_line_of_effort" varchar(255) null, add column "operational_rules" text[] null, add column "capability_area" text[] null, add column "feasibility_summary" varchar(255) null, add column "materiel_solution_type" varchar(255) null, add column "dotmlpfppchange" text[] null, add column "capability_sponsor" varchar(255) null, add column "army_capability_manager" text[] null, add column "existing_army_requirement" boolean not null default false;`);
    this.addSql(`alter table "opportunity" alter column "created_at" type timestamptz using ("created_at"::timestamptz);`);
    this.addSql(`alter table "opportunity" alter column "updated_at" type timestamptz using ("updated_at"::timestamptz);`);
    this.addSql(`alter table "opportunity" alter column "last_curated" type timestamptz using ("last_curated"::timestamptz);`);

    this.addSql(`alter table "related_opportunity" alter column "created_at" type timestamptz using ("created_at"::timestamptz);`);
    this.addSql(`alter table "related_opportunity" alter column "updated_at" type timestamptz using ("updated_at"::timestamptz);`);

    this.addSql(`alter table "curation_event" alter column "created_at" type timestamptz using ("created_at"::timestamptz);`);
    this.addSql(`alter table "curation_event" alter column "updated_at" type timestamptz using ("updated_at"::timestamptz);`);
    this.addSql(`alter table "curation_event" add constraint "curation_event_entity_type_check" check("entity_type" in ('o', 'u', 'p', 'l', 'a'));`);

    this.addSql(`alter table "attachment" add column "display_name" varchar(255) null, add column "notes" varchar(255) null, add column "created_by_id" uuid null;`);
    this.addSql(`alter table "attachment" alter column "created_at" type timestamptz using ("created_at"::timestamptz);`);
    this.addSql(`alter table "attachment" alter column "updated_at" type timestamptz using ("updated_at"::timestamptz);`);
    this.addSql(`alter table "attachment" add constraint "attachment_created_by_id_foreign" foreign key ("created_by_id") references "user" ("id") on update cascade on delete set null;`);
  }

  override async down(): Promise<void> {
    this.addSql(`alter table "opportunity_owner_status" drop constraint "opportunity_owner_status_owner_id_foreign";`);

    this.addSql(`drop table if exists "owner" cascade;`);

    this.addSql(`drop table if exists "requirement" cascade;`);

    this.addSql(`drop table if exists "opportunity_owner_status" cascade;`);

    this.addSql(`drop table if exists "existing_solution" cascade;`);

    this.addSql(`drop table if exists "link" cascade;`);

    this.addSql(`drop table if exists "change_event" cascade;`);

    this.addSql(`alter table "attachment" drop constraint "attachment_created_by_id_foreign";`);

    this.addSql(`alter table "curation_event" drop constraint if exists "curation_event_entity_type_check";`);

    this.addSql(`alter table "project_stakeholder" drop constraint if exists "project_stakeholder_type_check";`);

    this.addSql(`alter table "role" drop constraint if exists "role_name_check";`);

    this.addSql(`alter table "application_meta" alter column "created_at" type timestamptz(0) using ("created_at"::timestamptz(0));`);
    this.addSql(`alter table "application_meta" alter column "updated_at" type timestamptz(0) using ("updated_at"::timestamptz(0));`);

    this.addSql(`alter table "attachment" drop column "display_name", drop column "notes", drop column "created_by_id";`);

    this.addSql(`alter table "attachment" alter column "created_at" type timestamptz(0) using ("created_at"::timestamptz(0));`);
    this.addSql(`alter table "attachment" alter column "updated_at" type timestamptz(0) using ("updated_at"::timestamptz(0));`);

    this.addSql(`alter table "category" alter column "created_at" type timestamptz(0) using ("created_at"::timestamptz(0));`);
    this.addSql(`alter table "category" alter column "updated_at" type timestamptz(0) using ("updated_at"::timestamptz(0));`);

    this.addSql(`alter table "curation_event" alter column "created_at" type timestamptz(0) using ("created_at"::timestamptz(0));`);
    this.addSql(`alter table "curation_event" alter column "updated_at" type timestamptz(0) using ("updated_at"::timestamptz(0));`);
    this.addSql(`alter table "curation_event" add constraint "curation_event_entity_type_check" check("entity_type" in ('o', 'u', 'p'));`);

    this.addSql(`alter table "opportunity" drop column "is_ti_cloe", drop column "army_modernization_priority", drop column "echelon_applicability", drop column "transition_in_contact_line_of_effort", drop column "operational_rules", drop column "capability_area", drop column "feasibility_summary", drop column "materiel_solution_type", drop column "dotmlpfppchange", drop column "capability_sponsor", drop column "army_capability_manager", drop column "existing_army_requirement";`);

    this.addSql(`alter table "opportunity" alter column "created_at" type timestamptz(0) using ("created_at"::timestamptz(0));`);
    this.addSql(`alter table "opportunity" alter column "updated_at" type timestamptz(0) using ("updated_at"::timestamptz(0));`);
    this.addSql(`alter table "opportunity" alter column "last_curated" type timestamptz(0) using ("last_curated"::timestamptz(0));`);

    this.addSql(`alter table "privilege" alter column "created_at" type timestamptz(0) using ("created_at"::timestamptz(0));`);
    this.addSql(`alter table "privilege" alter column "updated_at" type timestamptz(0) using ("updated_at"::timestamptz(0));`);

    this.addSql(`alter table "privilege_group" alter column "created_at" type timestamptz(0) using ("created_at"::timestamptz(0));`);
    this.addSql(`alter table "privilege_group" alter column "updated_at" type timestamptz(0) using ("updated_at"::timestamptz(0));`);

    this.addSql(`alter table "project" alter column "created_at" type timestamptz(0) using ("created_at"::timestamptz(0));`);
    this.addSql(`alter table "project" alter column "updated_at" type timestamptz(0) using ("updated_at"::timestamptz(0));`);
    this.addSql(`alter table "project" alter column "last_curated" type timestamptz(0) using ("last_curated"::timestamptz(0));`);
    this.addSql(`alter table "project" alter column "start_date" type timestamptz(0) using ("start_date"::timestamptz(0));`);
    this.addSql(`alter table "project" alter column "end_date" type timestamptz(0) using ("end_date"::timestamptz(0));`);

    this.addSql(`alter table "project_stakeholder" alter column "created_at" type timestamptz(0) using ("created_at"::timestamptz(0));`);
    this.addSql(`alter table "project_stakeholder" alter column "updated_at" type timestamptz(0) using ("updated_at"::timestamptz(0));`);
    this.addSql(`alter table "project_stakeholder" add constraint "project_stakeholder_type_check" check("type" in ('division', 'performer', 'transition'));`);

    this.addSql(`alter table "related_opportunity" alter column "created_at" type timestamptz(0) using ("created_at"::timestamptz(0));`);
    this.addSql(`alter table "related_opportunity" alter column "updated_at" type timestamptz(0) using ("updated_at"::timestamptz(0));`);

    this.addSql(`alter table "role" alter column "created_at" type timestamptz(0) using ("created_at"::timestamptz(0));`);
    this.addSql(`alter table "role" alter column "updated_at" type timestamptz(0) using ("updated_at"::timestamptz(0));`);
    this.addSql(`alter table "role" add constraint "role_name_check" check("name" in ('curator', 'admin', 'analyst'));`);

    this.addSql(`alter table "stakeholder" drop column "first_name", drop column "last_name", drop column "title", drop column "phone", drop column "email_address", drop column "alt_email_address", drop column "organization_role";`);

    this.addSql(`alter table "stakeholder" alter column "created_at" type timestamptz(0) using ("created_at"::timestamptz(0));`);
    this.addSql(`alter table "stakeholder" alter column "updated_at" type timestamptz(0) using ("updated_at"::timestamptz(0));`);
    this.addSql(`alter table "stakeholder" add constraint "stakeholder_name_org_tenant_id_unique" unique ("name", "org", "tenant_id");`);

    this.addSql(`alter table "submission" alter column "created_at" type timestamptz(0) using ("created_at"::timestamptz(0));`);
    this.addSql(`alter table "submission" alter column "updated_at" type timestamptz(0) using ("updated_at"::timestamptz(0));`);

    this.addSql(`alter table "tenant" alter column "created_at" type timestamptz(0) using ("created_at"::timestamptz(0));`);
    this.addSql(`alter table "tenant" alter column "updated_at" type timestamptz(0) using ("updated_at"::timestamptz(0));`);

    this.addSql(`alter table "tenant_alias" alter column "created_at" type timestamptz(0) using ("created_at"::timestamptz(0));`);
    this.addSql(`alter table "tenant_alias" alter column "updated_at" type timestamptz(0) using ("updated_at"::timestamptz(0));`);

    this.addSql(`alter table "tenant_meta" alter column "created_at" type timestamptz(0) using ("created_at"::timestamptz(0));`);
    this.addSql(`alter table "tenant_meta" alter column "updated_at" type timestamptz(0) using ("updated_at"::timestamptz(0));`);

    this.addSql(`alter table "user" alter column "created_at" type timestamptz(0) using ("created_at"::timestamptz(0));`);
    this.addSql(`alter table "user" alter column "updated_at" type timestamptz(0) using ("updated_at"::timestamptz(0));`);
  }

}
